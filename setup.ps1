# Graphiti Full Stack Setup Script for Windows PowerShell

Write-Host "🚀 Setting up Graphiti Full Stack Application..." -ForegroundColor Green

# Check if Docker is installed
if (!(Get-Command docker -ErrorAction SilentlyContinue)) {
    Write-Host "❌ Docker is not installed. Please install Docker Desktop first." -ForegroundColor Red
    exit 1
}

# Check if Docker Compose is available
if (!(Get-Command docker-compose -ErrorAction SilentlyContinue)) {
    Write-Host "❌ Docker Compose is not available. Please install Docker Compose." -ForegroundColor Red
    exit 1
}

# Check if .env file exists
if (!(Test-Path ".env")) {
    Write-Host "❌ .env file not found. Please create a .env file with required environment variables." -ForegroundColor Red
    exit 1
}

Write-Host "✅ Prerequisites check passed!" -ForegroundColor Green

# Build and start services
Write-Host "🔨 Building and starting services..." -ForegroundColor Yellow
docker-compose down -v
docker-compose build --no-cache
docker-compose up -d

# Wait for services to be ready
Write-Host "⏳ Waiting for services to start..." -ForegroundColor Yellow
Start-Sleep -Seconds 30

# Check service health
Write-Host "🔍 Checking service health..." -ForegroundColor Yellow

# Check Neo4j
try {
    $neo4jResponse = Invoke-WebRequest -Uri "http://localhost:7892" -TimeoutSec 10
    Write-Host "✅ Neo4j is running on http://localhost:7892" -ForegroundColor Green
} catch {
    Write-Host "❌ Neo4j is not responding" -ForegroundColor Red
}

# Check Backend API
try {
    $backendResponse = Invoke-WebRequest -Uri "http://localhost:8234/healthcheck" -TimeoutSec 10
    Write-Host "✅ Backend API is running on http://localhost:8234" -ForegroundColor Green
} catch {
    Write-Host "❌ Backend API is not responding" -ForegroundColor Red
}

# Check Frontend
try {
    $frontendResponse = Invoke-WebRequest -Uri "http://localhost:3567" -TimeoutSec 10
    Write-Host "✅ Frontend is running on http://localhost:3567" -ForegroundColor Green
} catch {
    Write-Host "❌ Frontend is not responding" -ForegroundColor Red
}

Write-Host ""
Write-Host "🎉 Setup complete! Your Graphiti application is ready!" -ForegroundColor Green
Write-Host ""
Write-Host "Service URLs:" -ForegroundColor Cyan
Write-Host "   Frontend:     http://localhost:3567" -ForegroundColor White
Write-Host "   Backend API:  http://localhost:8234" -ForegroundColor White
Write-Host "   Neo4j Browser: http://localhost:7892" -ForegroundColor White
Write-Host ""
Write-Host "Useful commands:" -ForegroundColor Cyan
Write-Host "   View logs:    docker-compose logs -f" -ForegroundColor White
Write-Host "   Stop all:     docker-compose down" -ForegroundColor White
Write-Host "   Restart:      docker-compose restart" -ForegroundColor White
Write-Host ""
Write-Host "Neo4j Login:" -ForegroundColor Cyan
Write-Host "   Username: neo4j" -ForegroundColor White
Write-Host "   Password: Triathlon16" -ForegroundColor White
