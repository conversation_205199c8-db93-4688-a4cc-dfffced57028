import React from 'react';
import {
  AppB<PERSON>,
  Toolbar,
  Typo<PERSON>,
  Button,
  Box,
} from '@mui/material';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  Dashboard as DashboardIcon,
  AccountTree as GraphIcon,
  CloudUpload as IngestIcon,
  Search as SearchIcon,
  Chat as ChatIcon,
  UploadFile as UploadIcon,
} from '@mui/icons-material';

const Navbar: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();

  const navItems = [
    { path: '/', label: 'Dashboard', icon: <DashboardIcon /> },
    { path: '/chat', label: 'Chat', icon: <ChatIcon /> },
    { path: '/upload', label: 'Upload Docs', icon: <UploadIcon /> },
    { path: '/graph', label: 'Graph', icon: <GraphIcon /> },
    { path: '/ingest', label: 'Ingest Data', icon: <IngestIcon /> },
    { path: '/search', label: 'Search', icon: <SearchIcon /> },
  ];

  return (
    <AppBar position="static">
      <Toolbar>
        <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
          Graphiti Knowledge Graph
        </Typography>
        <Box sx={{ display: 'flex', gap: 1 }}>
          {navItems.map((item) => (
            <Button
              key={item.path}
              color="inherit"
              startIcon={item.icon}
              onClick={() => navigate(item.path)}
              sx={{
                backgroundColor: location.pathname === item.path ? 'rgba(255, 255, 255, 0.1)' : 'transparent',
              }}
            >
              {item.label}
            </Button>
          ))}
        </Box>
      </Toolbar>
    </AppBar>
  );
};

export default Navbar;
