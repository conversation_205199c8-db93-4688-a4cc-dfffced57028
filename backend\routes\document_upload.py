"""
Document upload routes for handling file uploads with Mistral OCR and entity extraction.
"""
import os
import base64
import tempfile
from typing import Optional
from fastapi import APIRouter, UploadFile, File, Form, HTTPException, BackgroundTasks
from fastapi.responses import JSONResponse
import logging

from graph_service.services.mistral_ocr_service import MistralOCRService
from graph_service.services.openrouter_service import OpenRouterService
from graph_service.zep_graphiti import ZepGraphitiDep
from graph_service.dto import AddMessagesRequest, AddEntityNodeRequest, Message, Result

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/documents", tags=["documents"])

# Initialize services
mistral_ocr = MistralOCRService()
openrouter = OpenRouterService()

@router.post("/upload")
async def upload_document(
    background_tasks: BackgroundTasks,
    file: UploadFile = File(...),
    group_id: str = Form(...),
    upload_type: str = Form(default="messages")
):
    """
    Upload and process a document with Mistral OCR and entity extraction.
    
    Args:
        file: The uploaded file
        group_id: Group ID for organizing the document
        upload_type: Either 'messages' or 'entities'
    
    Returns:
        JSON response with processing status
    """
    try:
        # Validate file
        if not file.filename:
            raise HTTPException(status_code=400, detail="No file provided")
        
        # Check file size (50MB limit for Mistral OCR)
        file_size = 0
        content = await file.read()
        file_size = len(content)
        
        if file_size > 50 * 1024 * 1024:  # 50MB
            raise HTTPException(status_code=400, detail="File size exceeds 50MB limit")
        
        # Reset file pointer
        await file.seek(0)
        
        # Determine file type and processing method
        file_extension = file.filename.lower().split('.')[-1] if '.' in file.filename else ''
        
        if file_extension in ['pdf', 'png', 'jpg', 'jpeg', 'avif', 'pptx', 'docx']:
            # Use Mistral OCR for supported formats
            logger.info(f"Processing {file.filename} with Mistral OCR")
            
            # Start background processing
            background_tasks.add_task(
                process_document_with_ocr,
                content,
                file.filename,
                file.content_type or 'application/octet-stream',
                group_id,
                upload_type
            )
            
            return JSONResponse(
                status_code=202,
                content={
                    "message": "Document upload started",
                    "filename": file.filename,
                    "status": "processing",
                    "processing_method": "mistral_ocr"
                }
            )
        
        else:
            # Use text extraction for other formats
            logger.info(f"Processing {file.filename} with text extraction")
            
            try:
                text_content = content.decode('utf-8')
            except UnicodeDecodeError:
                raise HTTPException(status_code=400, detail="Unable to decode file as text")
            
            # Start background processing
            background_tasks.add_task(
                process_document_with_text,
                text_content,
                file.filename,
                group_id,
                upload_type
            )
            
            return JSONResponse(
                status_code=202,
                content={
                    "message": "Document upload started",
                    "filename": file.filename,
                    "status": "processing",
                    "processing_method": "text_extraction"
                }
            )
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error uploading document: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Upload failed: {str(e)}")


async def process_document_with_ocr(
    content: bytes,
    filename: str,
    content_type: str,
    group_id: str,
    upload_type: str
):
    """
    Process document using Mistral OCR and extract entities.
    """
    try:
        logger.info(f"Starting OCR processing for {filename}")
        
        # Convert to base64 for Mistral OCR
        base64_content = base64.b64encode(content).decode('utf-8')
        
        # Determine document type for Mistral OCR
        if content_type == 'application/pdf' or filename.lower().endswith('.pdf'):
            document_url = f"data:application/pdf;base64,{base64_content}"
            doc_type = "document_url"
        elif content_type.startswith('image/') or filename.lower().endswith(('.png', '.jpg', '.jpeg', '.avif')):
            # Determine image type
            if filename.lower().endswith('.png'):
                mime_type = "image/png"
            elif filename.lower().endswith('.avif'):
                mime_type = "image/avif"
            else:
                mime_type = "image/jpeg"
            
            document_url = f"data:{mime_type};base64,{base64_content}"
            doc_type = "image_url"
        else:
            # For other document types (pptx, docx)
            document_url = f"data:{content_type};base64,{base64_content}"
            doc_type = "document_url"
        
        # Extract text using Mistral OCR
        extracted_text = await mistral_ocr.extract_text(document_url, doc_type)
        
        if not extracted_text:
            logger.error(f"No text extracted from {filename}")
            return
        
        logger.info(f"Extracted {len(extracted_text)} characters from {filename}")
        
        # Extract entities using OpenRouter
        entities = await openrouter.extract_entities(extracted_text)
        
        logger.info(f"Extracted {len(entities)} entities from {filename}")
        
        # Store in Graphiti based on upload type
        if upload_type == "messages":
            # Add as messages
            await apiService.addMessages({
                "group_id": group_id,
                "messages": [{
                    "name": f"Document: {filename}",
                    "content": extracted_text,
                    "role": "system",
                    "timestamp": "",  # Will be set by backend
                    "source_description": f"OCR extraction from {filename}",
                }]
            })
        else:
            # Add as entity nodes
            await apiService.addEntityNode({
                "group_id": group_id,
                "name": filename,
                "summary": extracted_text[:500] + ("..." if len(extracted_text) > 500 else ""),
            })
        
        # Store extracted entities
        for entity in entities:
            await apiService.addEntityNode({
                "group_id": group_id,
                "name": entity.get("name", "Unknown"),
                "summary": entity.get("description", ""),
            })
        
        logger.info(f"Successfully processed {filename}")
        
    except Exception as e:
        logger.error(f"Error processing document {filename}: {str(e)}")


async def process_document_with_text(
    text_content: str,
    filename: str,
    group_id: str,
    upload_type: str
):
    """
    Process document using text extraction and extract entities.
    """
    try:
        logger.info(f"Starting text processing for {filename}")
        
        # Extract entities using OpenRouter
        entities = await openrouter.extract_entities(text_content)
        
        logger.info(f"Extracted {len(entities)} entities from {filename}")
        
        # Store in Graphiti based on upload type
        if upload_type == "messages":
            # Add as messages
            await apiService.addMessages({
                "group_id": group_id,
                "messages": [{
                    "name": f"Document: {filename}",
                    "content": text_content,
                    "role": "system",
                    "timestamp": "",  # Will be set by backend
                    "source_description": f"Text extraction from {filename}",
                }]
            })
        else:
            # Add as entity nodes
            await apiService.addEntityNode({
                "group_id": group_id,
                "name": filename,
                "summary": text_content[:500] + ("..." if len(text_content) > 500 else ""),
            })
        
        # Store extracted entities
        for entity in entities:
            await apiService.addEntityNode({
                "group_id": group_id,
                "name": entity.get("name", "Unknown"),
                "summary": entity.get("description", ""),
            })
        
        logger.info(f"Successfully processed {filename}")
        
    except Exception as e:
        logger.error(f"Error processing document {filename}: {str(e)}")


@router.get("/upload/status/{filename}")
async def get_upload_status(filename: str):
    """
    Get the status of a document upload.
    """
    # This would typically check a database or cache for status
    # For now, return a simple response
    return {
        "filename": filename,
        "status": "completed",
        "message": "Document processed successfully"
    }
