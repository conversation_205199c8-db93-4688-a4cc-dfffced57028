import axios from 'axios';

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8234';

const apiClient = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor for debugging
apiClient.interceptors.request.use(
  (config) => {
    console.log('API Request:', config.method?.toUpperCase(), config.url);
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
apiClient.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    console.error('API Error:', error.response?.data || error.message);
    return Promise.reject(error);
  }
);

export interface Message {
  uuid?: string;
  name: string;
  content: string;
  role?: string;
  role_type?: string;
  timestamp?: string;
  source_description?: string;
}

export interface AddMessagesRequest {
  group_id: string;
  messages: Message[];
}

export interface AddEntityNodeRequest {
  uuid?: string;
  group_id: string;
  name: string;
  summary: string;
}

export interface SearchNodesRequest {
  query: string;
  group_ids?: string[];
  limit?: number;
}

export interface SearchFactsRequest {
  query: string;
  group_ids?: string[];
  limit?: number;
}

export const apiService = {
  // Health check
  healthCheck: async () => {
    const response = await apiClient.get('/healthcheck');
    return response.data;
  },

  // Messages
  addMessages: async (request: AddMessagesRequest) => {
    const response = await apiClient.post('/messages', request);
    return response.data;
  },

  // Entity nodes
  addEntityNode: async (request: AddEntityNodeRequest) => {
    const response = await apiClient.post('/entity-node', request);
    return response.data;
  },

  // Search
  searchNodes: async (request: SearchNodesRequest) => {
    const response = await apiClient.post('/search/nodes', request);
    return response.data;
  },

  searchFacts: async (request: SearchFactsRequest) => {
    const response = await apiClient.post('/search/facts', request);
    return response.data;
  },

  // Retrieval
  getEntityEdge: async (uuid: string) => {
    const response = await apiClient.get(`/entity-edge/${uuid}`);
    return response.data;
  },

  getEpisodes: async (groupId: string, lastN: number = 10) => {
    const response = await apiClient.get(`/episodes/${groupId}?last_n=${lastN}`);
    return response.data;
  },

  // Deletion
  deleteEntityEdge: async (uuid: string) => {
    const response = await apiClient.delete(`/entity-edge/${uuid}`);
    return response.data;
  },

  deleteGroup: async (groupId: string) => {
    const response = await apiClient.delete(`/group/${groupId}`);
    return response.data;
  },

  deleteEpisode: async (uuid: string) => {
    const response = await apiClient.delete(`/episode/${uuid}`);
    return response.data;
  },

  // Clear graph
  clearGraph: async () => {
    const response = await apiClient.post('/clear');
    return response.data;
  },
};
