import React, { useState, useRef } from 'react';
import {
  Container,
  Paper,
  Typography,
  Box,
  Button,
  Grid,
  Alert,
  LinearProgress,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  IconButton,
  Chip,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
} from '@mui/material';
import {
  CloudUpload as UploadIcon,
  Description as FileIcon,
  Delete as DeleteIcon,
  CheckCircle as SuccessIcon,
  Error as ErrorIcon,
} from '@mui/icons-material';
import { apiService } from '../services/apiService';

interface UploadFile {
  id: string;
  file: File;
  content?: string;
  status: 'pending' | 'reading' | 'uploading' | 'success' | 'error';
  error?: string;
  progress: number;
}

const DocumentUpload: React.FC = () => {
  const [files, setFiles] = useState<UploadFile[]>([]);
  const [groupId, setGroupId] = useState('document-upload');
  const [uploadType, setUploadType] = useState<'messages' | 'entities'>('messages');
  const [isUploading, setIsUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFiles = event.target.files;
    if (!selectedFiles) return;

    const newFiles: UploadFile[] = Array.from(selectedFiles).map(file => ({
      id: `${Date.now()}-${Math.random()}`,
      file,
      status: 'pending',
      progress: 0,
    }));

    setFiles(prev => [...prev, ...newFiles]);

    // Reset file input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }

    // Start reading files
    newFiles.forEach(uploadFile => {
      readFileContent(uploadFile);
    });
  };

  const readFileContent = (uploadFile: UploadFile) => {
    setFiles(prev => prev.map(f =>
      f.id === uploadFile.id ? { ...f, status: 'reading', progress: 25 } : f
    ));

    // For PDF files, we'll let the backend handle the content extraction
    if (uploadFile.file.type === 'application/pdf' || uploadFile.file.name.toLowerCase().endsWith('.pdf')) {
      setFiles(prev => prev.map(f =>
        f.id === uploadFile.id
          ? { ...f, content: '[PDF_FILE_BINARY_DATA]', status: 'pending', progress: 50 }
          : f
      ));
      return;
    }

    const reader = new FileReader();

    reader.onload = (e) => {
      const content = e.target?.result as string;
      setFiles(prev => prev.map(f =>
        f.id === uploadFile.id
          ? { ...f, content, status: 'pending', progress: 50 }
          : f
      ));
    };

    reader.onerror = () => {
      setFiles(prev => prev.map(f =>
        f.id === uploadFile.id
          ? { ...f, status: 'error', error: 'Failed to read file', progress: 0 }
          : f
      ));
    };

    reader.readAsText(uploadFile.file);
  };

  const removeFile = (fileId: string) => {
    setFiles(prev => prev.filter(f => f.id !== fileId));
  };

  const uploadFiles = async () => {
    if (files.length === 0) return;

    setIsUploading(true);

    for (const uploadFile of files) {
      if (uploadFile.status !== 'pending') continue;

      try {
        setFiles(prev => prev.map(f =>
          f.id === uploadFile.id ? { ...f, status: 'uploading', progress: 75 } : f
        ));

        // Create FormData for file upload
        const formData = new FormData();
        formData.append('file', uploadFile.file);
        formData.append('group_id', groupId);
        formData.append('upload_type', uploadType);

        // Upload file to backend for processing with comprehensive pipeline
        const response = await fetch('/api/documents/upload', {
          method: 'POST',
          body: formData,
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.detail || 'Upload failed');
        }

        const result = await response.json();



        setFiles(prev => prev.map(f =>
          f.id === uploadFile.id ? { ...f, status: 'success', progress: 100 } : f
        ));

      } catch (error: any) {
        setFiles(prev => prev.map(f =>
          f.id === uploadFile.id
            ? {
                ...f,
                status: 'error',
                error: error.message || 'Upload failed',
                progress: 0
              }
            : f
        ));
      }
    }

    setIsUploading(false);
  };

  const clearAllFiles = () => {
    setFiles([]);
  };

  const getStatusIcon = (status: UploadFile['status']) => {
    switch (status) {
      case 'success':
        return <SuccessIcon color="success" />;
      case 'error':
        return <ErrorIcon color="error" />;
      default:
        return <FileIcon />;
    }
  };

  const getStatusColor = (status: UploadFile['status']) => {
    switch (status) {
      case 'success':
        return 'success';
      case 'error':
        return 'error';
      case 'uploading':
      case 'reading':
        return 'primary';
      default:
        return 'default';
    }
  };

  const pendingFiles = files.filter(f => f.status === 'pending').length;
  const successFiles = files.filter(f => f.status === 'success').length;
  const errorFiles = files.filter(f => f.status === 'error').length;

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      <Typography variant="h4" component="h1" gutterBottom>
        Document Upload
      </Typography>

      <Grid container spacing={3}>
        {/* Upload Configuration */}
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Upload Settings
            </Typography>
            
            <TextField
              fullWidth
              label="Group ID"
              value={groupId}
              onChange={(e) => setGroupId(e.target.value)}
              sx={{ mb: 2 }}
              helperText="Documents will be grouped under this ID"
            />

            <FormControl fullWidth sx={{ mb: 3 }}>
              <InputLabel>Upload Type</InputLabel>
              <Select
                value={uploadType}
                label="Upload Type"
                onChange={(e) => setUploadType(e.target.value as 'messages' | 'entities')}
              >
                <MenuItem value="messages">As Messages</MenuItem>
                <MenuItem value="entities">As Entity Nodes</MenuItem>
              </Select>
            </FormControl>

            <input
              type="file"
              ref={fileInputRef}
              onChange={handleFileSelect}
              style={{ display: 'none' }}
              multiple
              accept=".txt,.md,.json,.csv,.pdf,.doc,.docx"
            />

            <Button
              fullWidth
              variant="outlined"
              startIcon={<UploadIcon />}
              onClick={() => fileInputRef.current?.click()}
              sx={{ mb: 2 }}
            >
              Select Files
            </Button>

            <Button
              fullWidth
              variant="contained"
              onClick={uploadFiles}
              disabled={pendingFiles === 0 || isUploading}
              sx={{ mb: 2 }}
            >
              Upload {pendingFiles} File{pendingFiles !== 1 ? 's' : ''}
            </Button>

            <Button
              fullWidth
              variant="text"
              onClick={clearAllFiles}
              disabled={files.length === 0}
            >
              Clear All
            </Button>

            {/* Upload Statistics */}
            {files.length > 0 && (
              <Box sx={{ mt: 3 }}>
                <Typography variant="subtitle2" gutterBottom>
                  Upload Statistics
                </Typography>
                <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                  <Chip label={`Total: ${files.length}`} size="small" />
                  <Chip label={`Pending: ${pendingFiles}`} size="small" color="default" />
                  <Chip label={`Success: ${successFiles}`} size="small" color="success" />
                  <Chip label={`Error: ${errorFiles}`} size="small" color="error" />
                </Box>
              </Box>
            )}
          </Paper>
        </Grid>

        {/* File List */}
        <Grid item xs={12} md={8}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Selected Files
            </Typography>

            {files.length === 0 ? (
              <Box
                sx={{
                  textAlign: 'center',
                  py: 8,
                  color: 'text.secondary',
                }}
              >
                <UploadIcon sx={{ fontSize: 64, mb: 2 }} />
                <Typography variant="h6" gutterBottom>
                  No files selected
                </Typography>
                <Typography variant="body2">
                  Click "Select Files" to choose documents to upload
                </Typography>
              </Box>
            ) : (
              <List>
                {files.map((uploadFile) => (
                  <ListItem
                    key={uploadFile.id}
                    sx={{
                      border: 1,
                      borderColor: 'divider',
                      borderRadius: 1,
                      mb: 1,
                    }}
                  >
                    <ListItemIcon>
                      {getStatusIcon(uploadFile.status)}
                    </ListItemIcon>
                    <ListItemText
                      primary={uploadFile.file.name}
                      secondary={
                        <Box>
                          <Typography variant="body2" color="text.secondary">
                            {(uploadFile.file.size / 1024).toFixed(1)} KB • {uploadFile.file.type || 'Unknown type'}
                          </Typography>
                          {uploadFile.error && (
                            <Typography variant="body2" color="error">
                              {uploadFile.error}
                            </Typography>
                          )}
                          {(uploadFile.status === 'reading' || uploadFile.status === 'uploading') && (
                            <LinearProgress 
                              variant="determinate" 
                              value={uploadFile.progress} 
                              sx={{ mt: 1 }}
                            />
                          )}
                        </Box>
                      }
                    />
                    <Chip
                      label={uploadFile.status}
                      size="small"
                      color={getStatusColor(uploadFile.status) as any}
                      sx={{ mr: 1 }}
                    />
                    <IconButton
                      onClick={() => removeFile(uploadFile.id)}
                      disabled={uploadFile.status === 'uploading'}
                    >
                      <DeleteIcon />
                    </IconButton>
                  </ListItem>
                ))}
              </List>
            )}
          </Paper>
        </Grid>

        {/* Service Status */}
        <Grid item xs={12}>
          <Alert severity="info" sx={{ mb: 2 }}>
            <Typography variant="subtitle2" gutterBottom>
              📊 Document Processing Pipeline Status:
            </Typography>
            <Typography variant="body2">
              ✅ **Comprehensive Document Processing**: Advanced OCR and entity extraction pipeline implemented<br/>
              ✅ **Mistral OCR Integration**: PDF and image text extraction with advanced analysis capabilities<br/>
              ✅ **OpenRouter Entity Extraction**: Medical/scientific entity extraction using meta-llama/llama-4-maverick<br/>
              ✅ **Knowledge Graph Integration**: Automatic storage in Neo4j with relationship mapping
            </Typography>
          </Alert>

          <Alert severity="info">
            <Typography variant="subtitle2" gutterBottom>
              Supported File Types:
            </Typography>
            <Typography variant="body2">
              • Text files (.txt, .md)
              • JSON files (.json)
              • CSV files (.csv)
              • PDF files (.pdf) - OCR processing coming soon
              • Word documents (.doc, .docx) - OCR processing coming soon
            </Typography>
            <Typography variant="body2" sx={{ mt: 1 }}>
              <strong>Upload as Messages:</strong> Documents are added as conversation messages in your knowledge graph.
              <br />
              <strong>Upload as Entity Nodes:</strong> Documents are created as entity nodes with their content as summaries.
            </Typography>
          </Alert>
        </Grid>
      </Grid>
    </Container>
  );
};

export default DocumentUpload;
