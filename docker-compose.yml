version: "3.8"

services:
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "3567:3000"
    environment:
      - REACT_APP_API_URL=http://localhost:8234
      - REACT_APP_NEO4J_BROWSER_URL=http://localhost:7892
    depends_on:
      - graph
    volumes:
      - ./frontend:/app
      - /app/node_modules

  graph:
    build:
      context: .
    ports:
      - "8234:8000"
    healthcheck:
      test:
        [
          "CMD",
          "python",
          "-c",
          "import urllib.request; urllib.request.urlopen('http://localhost:8000/healthcheck')",
        ]
      interval: 10s
      timeout: 5s
      retries: 3
    depends_on:
      - neo4j
    environment:
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - MISTRAL_API_KEY=${MISTRAL_API_KEY}
      - OPENROUTER_API_KEY=${OPENROUTER_API_KEY}
      - NEO4J_URI=bolt://neo4j:7687
      - NEO4J_USER=${NEO4J_USER}
      - NEO4J_PASSWORD=${NEO4J_PASSWORD}
      - PORT=8000
      # Document Processing Configuration
      - USE_MISTRAL_OCR=${USE_MISTRAL_OCR}
      - MISTRAL_OCR_MODEL=${MISTRAL_OCR_MODEL}
      - ENTITY_EXTRACTION_PROVIDER=${ENTITY_EXTRACTION_PROVIDER}
      - ENTITY_EXTRACTION_MODEL=${ENTITY_EXTRACTION_MODEL}
  neo4j:
    image: neo4j:5.26.2
    healthcheck:
      test: wget "http://localhost:7474" || exit 1
      interval: 1s
      timeout: 10s
      retries: 20
      start_period: 3s
    ports:
      - "7892:7474" # HTTP
      - "7891:7687" # Bolt
    volumes:
      - neo4j_data:/data
    environment:
      - NEO4J_AUTH=${NEO4J_USER}/${NEO4J_PASSWORD}

volumes:
  neo4j_data:
