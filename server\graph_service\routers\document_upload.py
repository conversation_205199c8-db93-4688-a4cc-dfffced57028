"""
Document upload routes for handling file uploads with comprehensive document processing.
"""
import logging
from datetime import datetime

from fastapi import APIRouter, UploadFile, File, Form, HTTPException, BackgroundTasks
from fastapi.responses import JSONResponse
from graphiti_core.nodes import EpisodeType

from graph_service.zep_graphiti import ZepGraphitiDep
from graph_service.services.document_processing_service import DocumentProcessingService

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/documents", tags=["documents"])

# Initialize document processing service
doc_processor = DocumentProcessingService()

@router.post("/upload")
async def upload_document(
    background_tasks: BackgroundTasks,
    file: UploadFile = File(...),
    group_id: str = Form(...),
    upload_type: str = Form(default="messages"),
    graphiti: ZepGraphitiDep = None
):
    """
    Upload and process a document with Mistral OCR and entity extraction.
    
    Args:
        file: The uploaded file
        group_id: Group ID for organizing the document
        upload_type: Either 'messages' or 'entities'
        graphiti: Graphiti dependency injection
    
    Returns:
        JSON response with processing status
    """
    try:
        # Validate file
        if not file.filename:
            raise HTTPException(status_code=400, detail="No file provided")
        
        # Check file size (50MB limit for Mistral OCR)
        content = await file.read()
        file_size = len(content)
        
        if file_size > 50 * 1024 * 1024:  # 50MB
            raise HTTPException(status_code=400, detail="File size exceeds 50MB limit")
        
        # Use the comprehensive document processing service
        logger.info(f"Processing {file.filename} with comprehensive document processing")

        # Start background processing
        background_tasks.add_task(
            process_document_comprehensive,
            content,
            file.filename,
            group_id,
            upload_type,
            graphiti
        )

        return JSONResponse(
            status_code=202,
            content={
                "message": "Document upload started",
                "filename": file.filename,
                "status": "processing",
                "processing_method": "comprehensive_pipeline"
            }
        )
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error uploading document: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Upload failed: {str(e)}")


async def process_document_comprehensive(
    content: bytes,
    filename: str,
    group_id: str,
    upload_type: str,
    graphiti
):
    """
    Process document using the comprehensive document processing service.
    """
    try:
        logger.info(f"Starting comprehensive processing for {filename}")

        # Use the document processing service
        result = await doc_processor.process_document(content, filename, upload_type)

        if not result.get("success"):
            logger.error(f"Document processing failed for {filename}: {result.get('error')}")
            return

        extracted_text = result.get("extracted_text", "")
        entities = result.get("entities", [])

        logger.info(f"Processed {filename}: {len(extracted_text)} characters, {len(entities)} entities")

        # Store in Graphiti based on upload type
        if upload_type == "messages":
            # Add as episode (message)
            await graphiti.add_episode(
                group_id=group_id,
                name=f"Document: {filename}",
                episode_body=extracted_text,
                reference_time=datetime.now(),
                source=EpisodeType.message,
                source_description=f"Comprehensive processing from {filename}",
            )
        else:
            # Add as entity node
            await graphiti.save_entity_node(
                group_id=group_id,
                name=filename,
                summary=extracted_text[:500] + ("..." if len(extracted_text) > 500 else ""),
            )

        # Store extracted entities with enhanced context
        for entity in entities:
            await graphiti.save_entity_node(
                group_id=group_id,
                name=entity.get("name", "Unknown"),
                summary=f"{entity.get('description', '')} (Context: {entity.get('context', '')})",
            )

        logger.info(f"Successfully processed {filename} with comprehensive pipeline")

    except Exception as e:
        logger.error(f"Error in comprehensive processing for {filename}: {str(e)}")


@router.get("/upload/status/{filename}")
async def get_upload_status(filename: str):
    """
    Get the status of a document upload.
    """
    # This would typically check a database or cache for status
    # For now, return a simple response
    return {
        "filename": filename,
        "status": "completed",
        "message": "Document processed successfully"
    }


@router.get("/services/status")
async def get_services_status():
    """
    Get the status of document processing services.
    """
    return await doc_processor.get_service_status()
