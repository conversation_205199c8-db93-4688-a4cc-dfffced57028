"""
Mistral OCR Service for extracting text from documents and images.
"""
import os
import asyncio
import logging
from typing import Optional, Dict, Any
from mistralai import Mistral

logger = logging.getLogger(__name__)

class MistralOCRService:
    """Service for handling Mistral OCR operations."""
    
    def __init__(self):
        """Initialize the Mistral OCR service."""
        self.api_key = os.getenv("MISTRAL_API_KEY")
        if not self.api_key:
            logger.warning("MISTRAL_API_KEY not found in environment variables")
            self.client = None
        else:
            self.client = Mistral(api_key=self.api_key)
            logger.info("Mistral OCR service initialized successfully")
    
    async def extract_text(self, document_url: str, doc_type: str = "document_url") -> Optional[str]:
        """
        Extract text from a document using Mistral OCR with comprehensive analysis capabilities.

        Args:
            document_url: Base64 encoded document URL or regular URL
            doc_type: Type of document ("document_url" or "image_url")

        Returns:
            Extracted text content or None if extraction fails
        """
        if not self.client:
            logger.error("Mistral client not initialized - missing API key")
            return None

        try:
            logger.info(f"Starting comprehensive OCR extraction for document type: {doc_type}")

            # Prepare document payload
            if doc_type == "image_url":
                document_payload = {
                    "type": "image_url",
                    "image_url": document_url
                }
            else:
                document_payload = {
                    "type": "document_url",
                    "document_url": document_url
                }

            # Run OCR processing in a thread pool to avoid blocking
            loop = asyncio.get_event_loop()
            ocr_response = await loop.run_in_executor(
                None,
                lambda: self.client.ocr.process(
                    model="mistral-ocr-latest",
                    document=document_payload,
                    include_image_base64=False  # We don't need images back
                )
            )

            # Extract text content from response
            if hasattr(ocr_response, 'content') and ocr_response.content:
                extracted_text = ocr_response.content
                logger.info(f"Successfully extracted {len(extracted_text)} characters with comprehensive analysis")
                return extracted_text

            elif hasattr(ocr_response, 'text') and ocr_response.text:
                extracted_text = ocr_response.text
                logger.info(f"Successfully extracted {len(extracted_text)} characters with comprehensive analysis")
                return extracted_text

            else:
                logger.warning("No text content found in OCR response")
                logger.debug(f"OCR response structure: {dir(ocr_response)}")
                return None

        except Exception as e:
            logger.error(f"Error during comprehensive OCR extraction: {str(e)}")
            return None

    def _create_comprehensive_ocr_prompt(self) -> str:
        """
        Create a comprehensive OCR prompt based on the advanced capabilities framework.

        Returns:
            Comprehensive OCR prompt for enhanced text extraction
        """
        return """
You are a helpful multi-modal AI assistant with advanced capabilities in:
1. Vision Processing: Analyzing and understanding images, photos, diagrams, charts, and video frames.
2. Text Processing: Interpreting, summarizing, and responding to text in multiple languages.
3. Graphics Understanding: Analyzing visual design elements, layouts, interfaces, and data visualizations.
4. OCR (Optical Character Recognition): Extracting text from images, documents, screenshots, and handwritten content.

Interaction Approach:
• Proactive Analysis: When presented with visual content, automatically identify and analyze key elements without requiring specific instructions.
• Detail-Oriented: Provide thorough descriptions of visual content while prioritizing relevant information based on context.
• Multi-Step Processing: For complex tasks, break down your approach into clear steps and explain your reasoning process.
• Context Awareness: Consider previous exchanges in the conversation when interpreting new requests or images.

Vision Processing Guidelines:
When analyzing visual content:
• Describe the main subject and prominent elements first
• Identify people, objects, scenes, actions, and environmental details
• Note significant colors, lighting, composition, and perspective
• Recognize emotional content or mood conveyed by images
• Identify brand logos, recognizable locations, or distinctive visual elements
• Detect potential safety concerns, inappropriate content, or sensitive material

OCR Implementation:
When extracting text from images:
• Maintain original formatting where relevant (columns, paragraphs, bullet points)
• Preserve text hierarchy (titles, headings, body text)
• Handle special characters, numbers, and symbols accurately
• Note text that appears unclear, partially visible, or potentially inaccurate
• For tables, preserve structure and relationships between data points
• For handwritten text, indicate confidence levels in transcription

Graphics Analysis Approach:
When examining graphic designs, interfaces, or visualizations:
• Identify design elements (typography, color schemes, layout principles)
• Analyze user interface components (buttons, menus, navigation elements)
• Interpret data visualizations (charts, graphs, maps)
• Recognize design patterns and potential usability considerations
• Comment on visual hierarchy, information architecture, and composition

Task-Specific Behaviors:

Document Processing:
• Extract key information from documents (receipts, invoices, forms)
• Identify document type, structure, and purpose
• Locate and extract specific data points (dates, amounts, names)

Interface Analysis:
• Assess software interfaces, websites, and digital products
• Identify UI elements, their functions, and relationships
• Comment on usability, accessibility, and design quality

Data Visualization Interpretation:
• Translate visual data representations into clear insights
• Identify trends, patterns, outliers, and relationships in data
• Explain complex visualizations in accessible language

Image-Based Problem Solving:
• Apply reasoning to visual puzzles, diagrams, or instructions
• Connect visual information with textual context
• Solve problems that require integrating visual and textual information

Please extract all text content from this document with maximum accuracy and attention to formatting, structure, and context.
"""
    
    async def extract_text_with_metadata(self, document_url: str, doc_type: str = "document_url") -> Optional[Dict[str, Any]]:
        """
        Extract text and metadata from a document using Mistral OCR.
        
        Args:
            document_url: Base64 encoded document URL or regular URL
            doc_type: Type of document ("document_url" or "image_url")
        
        Returns:
            Dictionary containing text content and metadata, or None if extraction fails
        """
        if not self.client:
            logger.error("Mistral client not initialized - missing API key")
            return None
        
        try:
            logger.info(f"Starting OCR extraction with metadata for document type: {doc_type}")
            
            # Prepare document payload
            if doc_type == "image_url":
                document_payload = {
                    "type": "image_url",
                    "image_url": document_url
                }
            else:
                document_payload = {
                    "type": "document_url",
                    "document_url": document_url
                }
            
            # Run OCR processing in a thread pool to avoid blocking
            loop = asyncio.get_event_loop()
            ocr_response = await loop.run_in_executor(
                None,
                lambda: self.client.ocr.process(
                    model="mistral-ocr-latest",
                    document=document_payload,
                    include_image_base64=True  # Include images for metadata
                )
            )
            
            # Extract content and metadata
            result = {
                "text": None,
                "metadata": {},
                "images": [],
                "structure": {}
            }
            
            # Extract text content
            if hasattr(ocr_response, 'content') and ocr_response.content:
                result["text"] = ocr_response.content
            elif hasattr(ocr_response, 'text') and ocr_response.text:
                result["text"] = ocr_response.text
            
            # Extract metadata if available
            if hasattr(ocr_response, 'metadata'):
                result["metadata"] = ocr_response.metadata
            
            # Extract images if available
            if hasattr(ocr_response, 'images'):
                result["images"] = ocr_response.images
            
            # Extract structure information if available
            if hasattr(ocr_response, 'structure'):
                result["structure"] = ocr_response.structure
            
            if result["text"]:
                logger.info(f"Successfully extracted {len(result['text'])} characters with metadata")
                return result
            else:
                logger.warning("No text content found in OCR response")
                return None
        
        except Exception as e:
            logger.error(f"Error during OCR extraction with metadata: {str(e)}")
            return None
    
    def is_available(self) -> bool:
        """
        Check if the Mistral OCR service is available.
        
        Returns:
            True if service is available, False otherwise
        """
        return self.client is not None
    
    async def test_connection(self) -> bool:
        """
        Test the connection to Mistral OCR service.
        
        Returns:
            True if connection is successful, False otherwise
        """
        if not self.client:
            return False
        
        try:
            # Test with a simple image URL
            test_url = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="
            
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(
                None,
                lambda: self.client.ocr.process(
                    model="mistral-ocr-latest",
                    document={
                        "type": "image_url",
                        "image_url": test_url
                    },
                    include_image_base64=False
                )
            )

            return True
        
        except Exception as e:
            logger.error(f"Mistral OCR connection test failed: {str(e)}")
            return False
    
    def get_supported_formats(self) -> Dict[str, list]:
        """
        Get the list of supported file formats.
        
        Returns:
            Dictionary with supported formats categorized by type
        """
        return {
            "images": ["png", "jpg", "jpeg", "avif"],
            "documents": ["pdf", "pptx", "docx"],
            "max_size_mb": 50,
            "max_pages": 1000
        }
