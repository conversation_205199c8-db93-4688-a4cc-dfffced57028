import React, { useState, useEffect } from 'react';
import {
  Container,
  Grid,
  Paper,
  Typography,
  Box,
  Card,
  CardContent,
  CircularProgress,
} from '@mui/material';
import {
  AccountTree as GraphIcon,
  Storage as NodeIcon,
  Link as EdgeIcon,
  Group as GroupIcon,
} from '@mui/icons-material';
import { apiService } from '../services/apiService';

interface DashboardStats {
  totalNodes: number;
  totalEdges: number;
  totalGroups: number;
  recentEpisodes: number;
}

const Dashboard: React.FC = () => {
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchStats = async () => {
      try {
        setLoading(true);
        // For now, we'll use mock data since the API endpoints might not exist yet
        // In a real implementation, you would call actual API endpoints
        const mockStats: DashboardStats = {
          totalNodes: 0,
          totalEdges: 0,
          totalGroups: 0,
          recentEpisodes: 0,
        };
        setStats(mockStats);
      } catch (err) {
        setError('Failed to fetch dashboard statistics');
        console.error('Dashboard error:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchStats();
  }, []);

  const StatCard: React.FC<{
    title: string;
    value: number;
    icon: React.ReactNode;
    color: string;
  }> = ({ title, value, icon, color }) => (
    <Card sx={{ height: '100%' }}>
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Box sx={{ color, mr: 2 }}>{icon}</Box>
          <Typography variant="h6" component="div">
            {title}
          </Typography>
        </Box>
        <Typography variant="h3" component="div" sx={{ color }}>
          {loading ? <CircularProgress size={24} /> : value.toLocaleString()}
        </Typography>
      </CardContent>
    </Card>
  );

  if (error) {
    return (
      <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
        <Typography color="error" variant="h6">
          {error}
        </Typography>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      <Typography variant="h4" component="h1" gutterBottom>
        Dashboard
      </Typography>
      
      <Grid container spacing={3}>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Total Nodes"
            value={stats?.totalNodes || 0}
            icon={<NodeIcon fontSize="large" />}
            color="#1976d2"
          />
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Total Edges"
            value={stats?.totalEdges || 0}
            icon={<EdgeIcon fontSize="large" />}
            color="#dc004e"
          />
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Groups"
            value={stats?.totalGroups || 0}
            icon={<GroupIcon fontSize="large" />}
            color="#ed6c02"
          />
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Recent Episodes"
            value={stats?.recentEpisodes || 0}
            icon={<GraphIcon fontSize="large" />}
            color="#2e7d32"
          />
        </Grid>

        <Grid item xs={12}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Welcome to Graphiti
            </Typography>
            <Typography variant="body1" paragraph>
              Graphiti is a temporal knowledge graph system that helps you build and query 
              dynamic knowledge representations. Use the navigation above to:
            </Typography>
            <Box component="ul" sx={{ pl: 2 }}>
              <li>
                <Typography variant="body2">
                  <strong>Graph:</strong> Visualize your knowledge graph structure
                </Typography>
              </li>
              <li>
                <Typography variant="body2">
                  <strong>Ingest Data:</strong> Add new episodes, messages, and entities
                </Typography>
              </li>
              <li>
                <Typography variant="body2">
                  <strong>Search:</strong> Find nodes, facts, and relationships
                </Typography>
              </li>
            </Box>
          </Paper>
        </Grid>
      </Grid>
    </Container>
  );
};

export default Dashboard;
