import React, { useState } from 'react';
import {
  Container,
  Paper,
  Typography,
  Box,
  TextField,
  Button,
  Grid,
  Alert,
  Tabs,
  Tab,
  CircularProgress,
} from '@mui/material';
import { useForm } from 'react-hook-form';
import { apiService, AddMessagesRequest, AddEntityNodeRequest, Message } from '../services/apiService';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

const DataIngestion: React.FC = () => {
  const [tabValue, setTabValue] = useState(0);
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  const messageForm = useForm<{
    groupId: string;
    name: string;
    content: string;
    role: string;
    roleType: string;
    sourceDescription: string;
  }>();

  const entityForm = useForm<{
    groupId: string;
    name: string;
    summary: string;
  }>();

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
    setSuccess(null);
    setError(null);
  };

  const onSubmitMessage = async (data: any) => {
    try {
      setLoading(true);
      setError(null);
      setSuccess(null);

      const message: Message = {
        name: data.name,
        content: data.content,
        role: data.role || undefined,
        role_type: data.roleType || undefined,
        timestamp: new Date().toISOString(),
        source_description: data.sourceDescription || undefined,
      };

      const request: AddMessagesRequest = {
        group_id: data.groupId,
        messages: [message],
      };

      await apiService.addMessages(request);
      setSuccess('Message added successfully!');
      messageForm.reset();
    } catch (err: any) {
      setError(err.response?.data?.detail || 'Failed to add message');
    } finally {
      setLoading(false);
    }
  };

  const onSubmitEntity = async (data: any) => {
    try {
      setLoading(true);
      setError(null);
      setSuccess(null);

      const request: AddEntityNodeRequest = {
        group_id: data.groupId,
        name: data.name,
        summary: data.summary,
      };

      await apiService.addEntityNode(request);
      setSuccess('Entity node added successfully!');
      entityForm.reset();
    } catch (err: any) {
      setError(err.response?.data?.detail || 'Failed to add entity node');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      <Typography variant="h4" component="h1" gutterBottom>
        Data Ingestion
      </Typography>

      <Paper sx={{ width: '100%' }}>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={tabValue} onChange={handleTabChange}>
            <Tab label="Add Message" />
            <Tab label="Add Entity Node" />
          </Tabs>
        </Box>

        {success && (
          <Alert severity="success" sx={{ m: 2 }}>
            {success}
          </Alert>
        )}

        {error && (
          <Alert severity="error" sx={{ m: 2 }}>
            {error}
          </Alert>
        )}

        <TabPanel value={tabValue} index={0}>
          <form onSubmit={messageForm.handleSubmit(onSubmitMessage)}>
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Group ID"
                  {...messageForm.register('groupId', { required: 'Group ID is required' })}
                  error={!!messageForm.formState.errors.groupId}
                  helperText={messageForm.formState.errors.groupId?.message}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Message Name"
                  {...messageForm.register('name', { required: 'Name is required' })}
                  error={!!messageForm.formState.errors.name}
                  helperText={messageForm.formState.errors.name?.message}
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  multiline
                  rows={4}
                  label="Content"
                  {...messageForm.register('content', { required: 'Content is required' })}
                  error={!!messageForm.formState.errors.content}
                  helperText={messageForm.formState.errors.content?.message}
                />
              </Grid>
              <Grid item xs={12} md={4}>
                <TextField
                  fullWidth
                  label="Role (optional)"
                  {...messageForm.register('role')}
                />
              </Grid>
              <Grid item xs={12} md={4}>
                <TextField
                  fullWidth
                  label="Role Type (optional)"
                  {...messageForm.register('roleType')}
                />
              </Grid>
              <Grid item xs={12} md={4}>
                <TextField
                  fullWidth
                  label="Source Description (optional)"
                  {...messageForm.register('sourceDescription')}
                />
              </Grid>
              <Grid item xs={12}>
                <Button
                  type="submit"
                  variant="contained"
                  disabled={loading}
                  startIcon={loading ? <CircularProgress size={20} /> : null}
                >
                  {loading ? 'Adding...' : 'Add Message'}
                </Button>
              </Grid>
            </Grid>
          </form>
        </TabPanel>

        <TabPanel value={tabValue} index={1}>
          <form onSubmit={entityForm.handleSubmit(onSubmitEntity)}>
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Group ID"
                  {...entityForm.register('groupId', { required: 'Group ID is required' })}
                  error={!!entityForm.formState.errors.groupId}
                  helperText={entityForm.formState.errors.groupId?.message}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Entity Name"
                  {...entityForm.register('name', { required: 'Name is required' })}
                  error={!!entityForm.formState.errors.name}
                  helperText={entityForm.formState.errors.name?.message}
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  multiline
                  rows={4}
                  label="Summary"
                  {...entityForm.register('summary', { required: 'Summary is required' })}
                  error={!!entityForm.formState.errors.summary}
                  helperText={entityForm.formState.errors.summary?.message}
                />
              </Grid>
              <Grid item xs={12}>
                <Button
                  type="submit"
                  variant="contained"
                  disabled={loading}
                  startIcon={loading ? <CircularProgress size={20} /> : null}
                >
                  {loading ? 'Adding...' : 'Add Entity Node'}
                </Button>
              </Grid>
            </Grid>
          </form>
        </TabPanel>
      </Paper>
    </Container>
  );
};

export default DataIngestion;
