"""
OpenRouter Service for entity extraction using meta-llama/llama-4-maverick.
"""
import os
import json
import asyncio
import logging
from typing import List, Dict, Any, Optional
import aiohttp

logger = logging.getLogger(__name__)

class OpenRouterService:
    """Service for handling OpenRouter LLM operations."""
    
    def __init__(self):
        """Initialize the OpenRouter service."""
        self.api_key = os.getenv("OPENROUTER_API_KEY")
        self.base_url = "https://openrouter.ai/api/v1"
        self.model = "meta-llama/llama-4-maverick"
        
        if not self.api_key:
            logger.warning("OPENROUTER_API_KEY not found in environment variables")
        else:
            logger.info("OpenRouter service initialized successfully")
    
    async def extract_entities(self, text: str) -> List[Dict[str, Any]]:
        """
        Extract entities from text using OpenRouter LLM.
        
        Args:
            text: Text content to extract entities from
        
        Returns:
            List of extracted entities with metadata
        """
        if not self.api_key:
            logger.error("OpenRouter API key not available")
            return []
        
        try:
            # Prepare the prompt for entity extraction
            prompt = self._create_entity_extraction_prompt(text)
            
            # Make API call to OpenRouter
            entities_response = await self._call_openrouter(prompt)
            
            if entities_response:
                # Parse the response to extract entities
                entities = self._parse_entities_response(entities_response)
                logger.info(f"Extracted {len(entities)} entities from text")
                return entities
            else:
                logger.warning("No response from OpenRouter API")
                return []
        
        except Exception as e:
            logger.error(f"Error extracting entities: {str(e)}")
            return []
    
    def _create_entity_extraction_prompt(self, text: str) -> str:
        """
        Create a prompt for entity extraction.
        
        Args:
            text: Input text
        
        Returns:
            Formatted prompt for entity extraction
        """
        prompt = f"""
You are an expert entity extraction system. Extract entities from the following text and categorize them according to the types below.

Entity Types:
- Herb: Medicinal plants and botanical remedies
- Nutrient: Vitamins, minerals, and nutritional compounds
- Disease: Medical conditions and disorders
- Medication: Pharmaceutical drugs and treatments
- Symptom: Clinical manifestations of conditions
- Process: Biological or chemical processes
- Treatment: Therapeutic approaches and interventions
- Research: Studies, trials, and research methodologies
- Organization: Research institutions, companies, and groups
- Person: Researchers, authors, and individuals
- Food: Dietary items and food groups
- Concept: Abstract ideas and theoretical constructs
- Location: Geographical places and regions
- Chemical: Chemical compounds and substances
- Protein: Protein molecules and structures
- Plant: Plant species and botanical classifications
- Ingredient: Components of formulations
- Hormone: Endocrine signaling molecules
- Study: Specific research studies and clinical trials

For each entity, provide:
1. name: The entity name
2. type: One of the types listed above
3. description: Brief description of the entity
4. confidence: Confidence score (0.0 to 1.0)

Return the results as a JSON array of objects. Example:
[
  {{
    "name": "Vitamin C",
    "type": "Nutrient",
    "description": "Essential vitamin for immune function and collagen synthesis",
    "confidence": 0.95
  }}
]

Text to analyze:
{text[:4000]}  # Limit text to avoid token limits

Return only the JSON array, no additional text.
"""
        return prompt
    
    async def _call_openrouter(self, prompt: str) -> Optional[str]:
        """
        Make an API call to OpenRouter.
        
        Args:
            prompt: The prompt to send
        
        Returns:
            Response text or None if failed
        """
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json",
            "HTTP-Referer": "https://graphiti-ui.local",  # Optional
            "X-Title": "Graphiti Entity Extraction"  # Optional
        }
        
        payload = {
            "model": self.model,
            "messages": [
                {
                    "role": "user",
                    "content": prompt
                }
            ],
            "temperature": 0.1,  # Low temperature for consistent extraction
            "max_tokens": 2000,
            "top_p": 0.9
        }
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.base_url}/chat/completions",
                    headers=headers,
                    json=payload,
                    timeout=aiohttp.ClientTimeout(total=60)
                ) as response:
                    
                    if response.status == 200:
                        data = await response.json()
                        if "choices" in data and len(data["choices"]) > 0:
                            return data["choices"][0]["message"]["content"]
                    else:
                        error_text = await response.text()
                        logger.error(f"OpenRouter API error {response.status}: {error_text}")
                        return None
        
        except asyncio.TimeoutError:
            logger.error("OpenRouter API request timed out")
            return None
        except Exception as e:
            logger.error(f"Error calling OpenRouter API: {str(e)}")
            return None
    
    def _parse_entities_response(self, response: str) -> List[Dict[str, Any]]:
        """
        Parse the entities response from the LLM.
        
        Args:
            response: Raw response from the LLM
        
        Returns:
            List of parsed entities
        """
        try:
            # Try to find JSON in the response
            response = response.strip()
            
            # Remove any markdown code blocks
            if response.startswith("```json"):
                response = response[7:]
            if response.startswith("```"):
                response = response[3:]
            if response.endswith("```"):
                response = response[:-3]
            
            response = response.strip()
            
            # Parse JSON
            entities = json.loads(response)
            
            # Validate and clean entities
            cleaned_entities = []
            for entity in entities:
                if isinstance(entity, dict) and "name" in entity:
                    cleaned_entity = {
                        "name": entity.get("name", "Unknown"),
                        "type": entity.get("type", "Concept"),
                        "description": entity.get("description", ""),
                        "confidence": float(entity.get("confidence", 0.5))
                    }
                    cleaned_entities.append(cleaned_entity)
            
            return cleaned_entities
        
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse JSON response: {str(e)}")
            logger.debug(f"Response was: {response}")
            return []
        except Exception as e:
            logger.error(f"Error parsing entities response: {str(e)}")
            return []
    
    async def extract_relationships(self, entities: List[Dict[str, Any]], text: str) -> List[Dict[str, Any]]:
        """
        Extract relationships between entities.
        
        Args:
            entities: List of entities to find relationships for
            text: Original text context
        
        Returns:
            List of relationships
        """
        if not self.api_key or len(entities) < 2:
            return []
        
        try:
            # Create relationship extraction prompt
            entity_names = [entity["name"] for entity in entities]
            prompt = self._create_relationship_extraction_prompt(entity_names, text)
            
            # Make API call
            response = await self._call_openrouter(prompt)
            
            if response:
                relationships = self._parse_relationships_response(response)
                logger.info(f"Extracted {len(relationships)} relationships")
                return relationships
            else:
                return []
        
        except Exception as e:
            logger.error(f"Error extracting relationships: {str(e)}")
            return []
    
    def _create_relationship_extraction_prompt(self, entity_names: List[str], text: str) -> str:
        """Create a prompt for relationship extraction."""
        entities_str = ", ".join(entity_names)
        
        prompt = f"""
Extract relationships between the following entities based on the provided text context.

Entities: {entities_str}

Relationship Types:
- IS_A: Hierarchical classification
- PART_OF: Component relationships
- TREATS: Therapeutic relationships
- CAUSES: Causal relationships
- PREVENTS: Preventative relationships
- CONTAINS: Compositional relationships
- INTERACTS_WITH: Interaction relationships
- INCREASES: Enhancement relationships
- DECREASES: Reduction relationships
- REGULATES: Control relationships

For each relationship, provide:
1. source: Source entity name
2. target: Target entity name
3. relationship: Relationship type
4. confidence: Confidence score (0.0 to 1.0)

Return as JSON array:
[
  {{
    "source": "Entity1",
    "target": "Entity2", 
    "relationship": "TREATS",
    "confidence": 0.85
  }}
]

Text context:
{text[:3000]}

Return only the JSON array, no additional text.
"""
        return prompt
    
    def _parse_relationships_response(self, response: str) -> List[Dict[str, Any]]:
        """Parse relationships from LLM response."""
        try:
            # Clean response
            response = response.strip()
            if response.startswith("```json"):
                response = response[7:]
            if response.startswith("```"):
                response = response[3:]
            if response.endswith("```"):
                response = response[:-3]
            response = response.strip()
            
            # Parse JSON
            relationships = json.loads(response)
            
            # Validate relationships
            cleaned_relationships = []
            for rel in relationships:
                if isinstance(rel, dict) and all(key in rel for key in ["source", "target", "relationship"]):
                    cleaned_rel = {
                        "source": rel["source"],
                        "target": rel["target"],
                        "relationship": rel["relationship"],
                        "confidence": float(rel.get("confidence", 0.5))
                    }
                    cleaned_relationships.append(cleaned_rel)
            
            return cleaned_relationships
        
        except Exception as e:
            logger.error(f"Error parsing relationships: {str(e)}")
            return []
    
    def is_available(self) -> bool:
        """Check if OpenRouter service is available."""
        return self.api_key is not None
    
    async def test_connection(self) -> bool:
        """Test connection to OpenRouter."""
        if not self.api_key:
            return False
        
        try:
            response = await self._call_openrouter("Test message. Respond with 'OK'.")
            return response is not None
        except Exception:
            return False
