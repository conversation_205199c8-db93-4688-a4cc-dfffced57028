import React, { useState, useRef, useEffect } from 'react';
import {
  Container,
  Paper,
  Typography,
  Box,
  TextField,
  Button,
  List,
  ListItem,
  Avatar,
  Chip,
  IconButton,
  CircularProgress,
  Alert,
} from '@mui/material';
import {
  Send as SendIcon,
  Person as PersonIcon,
  SmartToy as BotIcon,
  AttachFile as AttachFileIcon,
} from '@mui/icons-material';
import { apiService } from '../services/apiService';

interface ChatMessage {
  id: string;
  content: string;
  role: 'user' | 'assistant';
  timestamp: Date;
  isLoading?: boolean;
}

interface UploadedFile {
  name: string;
  size: number;
  type: string;
  content: string;
}

const Chat: React.FC = () => {
  const [messages, setMessages] = useState<ChatMessage[]>([
    {
      id: '1',
      content: 'Hello! I\'m your Graphiti AI assistant. I can help you with your knowledge graph. You can ask me questions, upload documents, or add information to your graph.',
      role: 'assistant',
      timestamp: new Date(),
    },
  ]);
  const [inputMessage, setInputMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [groupId, setGroupId] = useState('default-chat-group');
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);
  
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleSendMessage = async () => {
    if (!inputMessage.trim() && uploadedFiles.length === 0) return;

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      content: inputMessage,
      role: 'user',
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);
    setInputMessage('');
    setIsLoading(true);
    setError(null);

    try {
      // Add user message to the graph
      await apiService.addMessages({
        group_id: groupId,
        messages: [{
          name: 'User',
          content: inputMessage,
          role: 'user',
          timestamp: new Date().toISOString(),
        }],
      });

      // Process uploaded files if any
      if (uploadedFiles.length > 0) {
        for (const file of uploadedFiles) {
          await apiService.addMessages({
            group_id: groupId,
            messages: [{
              name: 'Document Upload',
              content: `Uploaded document: ${file.name}\n\nContent:\n${file.content}`,
              role: 'system',
              timestamp: new Date().toISOString(),
              source_description: `File upload: ${file.name} (${file.type})`,
            }],
          });
        }
        setUploadedFiles([]);
      }

      // Simulate AI response (in a real implementation, you would call your AI service)
      const aiResponse: ChatMessage = {
        id: (Date.now() + 1).toString(),
        content: generateAIResponse(inputMessage, uploadedFiles),
        role: 'assistant',
        timestamp: new Date(),
      };

      setTimeout(() => {
        setMessages(prev => [...prev, aiResponse]);
        
        // Add AI response to the graph
        apiService.addMessages({
          group_id: groupId,
          messages: [{
            name: 'AI Assistant',
            content: aiResponse.content,
            role: 'assistant',
            timestamp: new Date().toISOString(),
          }],
        });
      }, 1000);

    } catch (err: any) {
      setError(err.response?.data?.detail || 'Failed to send message');
    } finally {
      setIsLoading(false);
    }
  };

  const generateAIResponse = (userInput: string, files: UploadedFile[]): string => {
    // Simple response generation - in a real app, this would call your AI service
    if (files.length > 0) {
      return `I've received and processed ${files.length} document(s). The content has been added to your knowledge graph. You can now search for information from these documents or ask me questions about their content.`;
    }
    
    if (userInput.toLowerCase().includes('search')) {
      return 'I can help you search your knowledge graph. What specific information are you looking for?';
    }
    
    if (userInput.toLowerCase().includes('upload') || userInput.toLowerCase().includes('document')) {
      return 'You can upload documents by clicking the attachment icon. I support text files, PDFs, and other document formats.';
    }
    
    return `I understand you said: "${userInput}". I've added this to your knowledge graph. How else can I help you today?`;
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files) return;

    Array.from(files).forEach(file => {
      const reader = new FileReader();
      reader.onload = (e) => {
        const content = e.target?.result as string;
        const uploadedFile: UploadedFile = {
          name: file.name,
          size: file.size,
          type: file.type,
          content: content,
        };
        setUploadedFiles(prev => [...prev, uploadedFile]);
      };
      reader.readAsText(file);
    });

    // Reset file input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const removeUploadedFile = (index: number) => {
    setUploadedFiles(prev => prev.filter((_, i) => i !== index));
  };

  const handleKeyPress = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      handleSendMessage();
    }
  };

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4, height: 'calc(100vh - 200px)' }}>
      <Typography variant="h4" component="h1" gutterBottom>
        Chat with Graphiti AI
      </Typography>

      <Paper sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
        {/* Group ID Input */}
        <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
          <TextField
            size="small"
            label="Group ID"
            value={groupId}
            onChange={(e) => setGroupId(e.target.value)}
            sx={{ minWidth: 200 }}
            helperText="Messages will be grouped under this ID in your knowledge graph"
          />
        </Box>

        {/* Messages Area */}
        <Box sx={{ flex: 1, overflow: 'auto', p: 2 }}>
          <List>
            {messages.map((message) => (
              <ListItem
                key={message.id}
                sx={{
                  flexDirection: 'column',
                  alignItems: message.role === 'user' ? 'flex-end' : 'flex-start',
                  mb: 1,
                }}
              >
                <Box
                  sx={{
                    display: 'flex',
                    alignItems: 'flex-start',
                    flexDirection: message.role === 'user' ? 'row-reverse' : 'row',
                    maxWidth: '80%',
                  }}
                >
                  <Avatar
                    sx={{
                      bgcolor: message.role === 'user' ? 'primary.main' : 'secondary.main',
                      mx: 1,
                    }}
                  >
                    {message.role === 'user' ? <PersonIcon /> : <BotIcon />}
                  </Avatar>
                  <Paper
                    sx={{
                      p: 2,
                      bgcolor: message.role === 'user' ? 'primary.light' : 'grey.100',
                      color: message.role === 'user' ? 'primary.contrastText' : 'text.primary',
                    }}
                  >
                    <Typography variant="body1" sx={{ whiteSpace: 'pre-wrap' }}>
                      {message.content}
                    </Typography>
                    <Typography variant="caption" sx={{ opacity: 0.7, mt: 1, display: 'block' }}>
                      {message.timestamp.toLocaleTimeString()}
                    </Typography>
                  </Paper>
                </Box>
              </ListItem>
            ))}
            {isLoading && (
              <ListItem sx={{ justifyContent: 'center' }}>
                <CircularProgress size={24} />
                <Typography variant="body2" sx={{ ml: 1 }}>
                  Processing...
                </Typography>
              </ListItem>
            )}
          </List>
          <div ref={messagesEndRef} />
        </Box>

        {/* Error Display */}
        {error && (
          <Alert severity="error" sx={{ m: 2 }}>
            {error}
          </Alert>
        )}

        {/* Uploaded Files Display */}
        {uploadedFiles.length > 0 && (
          <Box sx={{ p: 2, borderTop: 1, borderColor: 'divider' }}>
            <Typography variant="subtitle2" gutterBottom>
              Uploaded Files:
            </Typography>
            <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
              {uploadedFiles.map((file, index) => (
                <Chip
                  key={index}
                  label={`${file.name} (${(file.size / 1024).toFixed(1)}KB)`}
                  onDelete={() => removeUploadedFile(index)}
                  color="primary"
                  variant="outlined"
                />
              ))}
            </Box>
          </Box>
        )}

        {/* Input Area */}
        <Box sx={{ p: 2, borderTop: 1, borderColor: 'divider' }}>
          <Box sx={{ display: 'flex', gap: 1, alignItems: 'flex-end' }}>
            <input
              type="file"
              ref={fileInputRef}
              onChange={handleFileUpload}
              style={{ display: 'none' }}
              multiple
              accept=".txt,.md,.json,.csv,.pdf"
            />
            <IconButton
              onClick={() => fileInputRef.current?.click()}
              color="primary"
              title="Upload documents"
            >
              <AttachFileIcon />
            </IconButton>
            <TextField
              fullWidth
              multiline
              maxRows={4}
              value={inputMessage}
              onChange={(e) => setInputMessage(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Type your message... (Press Enter to send, Shift+Enter for new line)"
              disabled={isLoading}
            />
            <Button
              variant="contained"
              onClick={handleSendMessage}
              disabled={isLoading || (!inputMessage.trim() && uploadedFiles.length === 0)}
              endIcon={<SendIcon />}
            >
              Send
            </Button>
          </Box>
        </Box>
      </Paper>
    </Container>
  );
};

export default Chat;
