import React, { useState } from 'react';
import {
  Container,
  Paper,
  Typography,
  Box,
  TextField,
  Button,
  Grid,
  Alert,
  Tabs,
  Tab,
  CircularProgress,
  Card,
  CardContent,
  Chip,
  List,
  ListItem,
  ListItemText,
} from '@mui/material';
import { Search as SearchIcon } from '@mui/icons-material';
import { apiService, SearchNodesRequest, SearchFactsRequest } from '../services/apiService';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`search-tabpanel-${index}`}
      aria-labelledby={`search-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

const Search: React.FC = () => {
  const [tabValue, setTabValue] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [nodeResults, setNodeResults] = useState<any[]>([]);
  const [factResults, setFactResults] = useState<any[]>([]);

  const [nodeQuery, setNodeQuery] = useState('');
  const [factQuery, setFactQuery] = useState('');
  const [groupIds, setGroupIds] = useState('');
  const [limit, setLimit] = useState(10);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
    setError(null);
  };

  const searchNodes = async () => {
    try {
      setLoading(true);
      setError(null);
      setNodeResults([]);

      const request: SearchNodesRequest = {
        query: nodeQuery,
        group_ids: groupIds ? groupIds.split(',').map(id => id.trim()) : undefined,
        limit,
      };

      const results = await apiService.searchNodes(request);
      setNodeResults(results);
    } catch (err: any) {
      setError(err.response?.data?.detail || 'Failed to search nodes');
    } finally {
      setLoading(false);
    }
  };

  const searchFacts = async () => {
    try {
      setLoading(true);
      setError(null);
      setFactResults([]);

      const request: SearchFactsRequest = {
        query: factQuery,
        group_ids: groupIds ? groupIds.split(',').map(id => id.trim()) : undefined,
        limit,
      };

      const results = await apiService.searchFacts(request);
      setFactResults(results);
    } catch (err: any) {
      setError(err.response?.data?.detail || 'Failed to search facts');
    } finally {
      setLoading(false);
    }
  };

  const ResultCard: React.FC<{ result: any; type: 'node' | 'fact' }> = ({ result, type }) => (
    <Card sx={{ mb: 2 }}>
      <CardContent>
        <Typography variant="h6" gutterBottom>
          {result.name || result.title || 'Unnamed'}
        </Typography>
        {result.summary && (
          <Typography variant="body2" color="text.secondary" paragraph>
            {result.summary}
          </Typography>
        )}
        {result.content && (
          <Typography variant="body2" color="text.secondary" paragraph>
            {result.content}
          </Typography>
        )}
        <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap', mt: 1 }}>
          {result.uuid && (
            <Chip label={`ID: ${result.uuid.substring(0, 8)}...`} size="small" />
          )}
          {result.group_id && (
            <Chip label={`Group: ${result.group_id}`} size="small" color="primary" />
          )}
          {result.score && (
            <Chip label={`Score: ${result.score.toFixed(3)}`} size="small" color="secondary" />
          )}
          {type === 'fact' && result.fact_type && (
            <Chip label={`Type: ${result.fact_type}`} size="small" color="info" />
          )}
        </Box>
      </CardContent>
    </Card>
  );

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      <Typography variant="h4" component="h1" gutterBottom>
        Search Knowledge Graph
      </Typography>

      <Paper sx={{ width: '100%' }}>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={tabValue} onChange={handleTabChange}>
            <Tab label="Search Nodes" />
            <Tab label="Search Facts" />
          </Tabs>
        </Box>

        {error && (
          <Alert severity="error" sx={{ m: 2 }}>
            {error}
          </Alert>
        )}

        <TabPanel value={tabValue} index={0}>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Search Query"
                value={nodeQuery}
                onChange={(e) => setNodeQuery(e.target.value)}
                placeholder="Enter your search query for nodes..."
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Group IDs (comma-separated, optional)"
                value={groupIds}
                onChange={(e) => setGroupIds(e.target.value)}
                placeholder="group1, group2, group3"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                type="number"
                label="Limit"
                value={limit}
                onChange={(e) => setLimit(parseInt(e.target.value) || 10)}
                inputProps={{ min: 1, max: 100 }}
              />
            </Grid>
            <Grid item xs={12}>
              <Button
                variant="contained"
                startIcon={loading ? <CircularProgress size={20} /> : <SearchIcon />}
                onClick={searchNodes}
                disabled={loading || !nodeQuery.trim()}
              >
                {loading ? 'Searching...' : 'Search Nodes'}
              </Button>
            </Grid>
            <Grid item xs={12}>
              {nodeResults.length > 0 && (
                <Box>
                  <Typography variant="h6" gutterBottom>
                    Search Results ({nodeResults.length})
                  </Typography>
                  {nodeResults.map((result, index) => (
                    <ResultCard key={index} result={result} type="node" />
                  ))}
                </Box>
              )}
            </Grid>
          </Grid>
        </TabPanel>

        <TabPanel value={tabValue} index={1}>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Search Query"
                value={factQuery}
                onChange={(e) => setFactQuery(e.target.value)}
                placeholder="Enter your search query for facts/relationships..."
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Group IDs (comma-separated, optional)"
                value={groupIds}
                onChange={(e) => setGroupIds(e.target.value)}
                placeholder="group1, group2, group3"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                type="number"
                label="Limit"
                value={limit}
                onChange={(e) => setLimit(parseInt(e.target.value) || 10)}
                inputProps={{ min: 1, max: 100 }}
              />
            </Grid>
            <Grid item xs={12}>
              <Button
                variant="contained"
                startIcon={loading ? <CircularProgress size={20} /> : <SearchIcon />}
                onClick={searchFacts}
                disabled={loading || !factQuery.trim()}
              >
                {loading ? 'Searching...' : 'Search Facts'}
              </Button>
            </Grid>
            <Grid item xs={12}>
              {factResults.length > 0 && (
                <Box>
                  <Typography variant="h6" gutterBottom>
                    Search Results ({factResults.length})
                  </Typography>
                  {factResults.map((result, index) => (
                    <ResultCard key={index} result={result} type="fact" />
                  ))}
                </Box>
              )}
            </Grid>
          </Grid>
        </TabPanel>
      </Paper>
    </Container>
  );
};

export default Search;
