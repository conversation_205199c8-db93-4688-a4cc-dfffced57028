"""
Document Processing Service

Orchestrates the complete document processing pipeline:
1. OCR text extraction using Mistral OCR
2. Entity extraction using OpenRouter
3. Integration with Graphiti knowledge graph
"""

import asyncio
import logging
import tempfile
import os
from typing import Dict, List, Optional, Any
from pathlib import Path

from .mistral_ocr_service import MistralOCRService
from .openrouter_service import OpenRouterService

logger = logging.getLogger(__name__)


class DocumentProcessingService:
    """
    Service for processing documents through the complete pipeline:
    Mistral OCR → Entity Extraction → Knowledge Graph Integration
    """
    
    def __init__(self):
        """Initialize the document processing service."""
        self.ocr_service = MistralOCRService()
        self.entity_service = OpenRouterService()
        
    async def process_document(
        self, 
        file_content: bytes, 
        filename: str, 
        upload_mode: str = "messages"
    ) -> Dict[str, Any]:
        """
        Process a document through the complete pipeline.
        
        Args:
            file_content: Raw file content as bytes
            filename: Original filename
            upload_mode: How to process the document ("messages" or "entities")
        
        Returns:
            Processing results including extracted text, entities, and status
        """
        try:
            logger.info(f"Starting document processing for {filename} in {upload_mode} mode")
            
            # Step 1: Determine file type and processing method
            file_extension = Path(filename).suffix.lower()
            
            if file_extension == '.pdf':
                # Use OCR for PDF files
                extracted_text = await self._process_pdf_with_ocr(file_content, filename)
            elif file_extension in ['.txt', '.md']:
                # Direct text extraction for text files
                extracted_text = file_content.decode('utf-8')
            elif file_extension in ['.doc', '.docx']:
                # For now, treat as text (future: implement proper Word processing)
                try:
                    extracted_text = file_content.decode('utf-8')
                except UnicodeDecodeError:
                    logger.warning(f"Could not decode {filename} as text, using OCR")
                    extracted_text = await self._process_pdf_with_ocr(file_content, filename)
            else:
                # Try OCR for other file types
                extracted_text = await self._process_pdf_with_ocr(file_content, filename)
            
            if not extracted_text:
                return {
                    "success": False,
                    "error": "Failed to extract text from document",
                    "filename": filename
                }
            
            logger.info(f"Extracted {len(extracted_text)} characters from {filename}")
            
            # Step 2: Extract entities if in entities mode
            entities = []
            if upload_mode == "entities":
                logger.info("Extracting entities from document text")
                entities = await self.entity_service.extract_entities(extracted_text)
                logger.info(f"Extracted {len(entities)} entities")
            
            # Step 3: Prepare results
            result = {
                "success": True,
                "filename": filename,
                "extracted_text": extracted_text,
                "text_length": len(extracted_text),
                "entities": entities,
                "upload_mode": upload_mode,
                "processing_steps": {
                    "ocr_completed": True,
                    "entity_extraction_completed": upload_mode == "entities",
                    "entities_count": len(entities)
                }
            }
            
            logger.info(f"Successfully processed {filename}")
            return result
            
        except Exception as e:
            logger.error(f"Error processing document {filename}: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "filename": filename
            }
    
    async def _process_pdf_with_ocr(self, file_content: bytes, filename: str) -> Optional[str]:
        """
        Process a PDF or image file using Mistral OCR.
        
        Args:
            file_content: Raw file content
            filename: Original filename
        
        Returns:
            Extracted text or None if extraction fails
        """
        try:
            # Create a temporary file for OCR processing
            with tempfile.NamedTemporaryFile(delete=False, suffix=Path(filename).suffix) as temp_file:
                temp_file.write(file_content)
                temp_file_path = temp_file.name
            
            try:
                # Convert file to base64 for Mistral OCR
                import base64
                with open(temp_file_path, 'rb') as f:
                    file_base64 = base64.b64encode(f.read()).decode('utf-8')
                
                # Determine document type based on file extension
                file_extension = Path(filename).suffix.lower()
                if file_extension in ['.jpg', '.jpeg', '.png', '.gif', '.bmp']:
                    doc_type = "image_url"
                    document_url = f"data:image/{file_extension[1:]};base64,{file_base64}"
                else:
                    doc_type = "document_url"
                    document_url = f"data:application/pdf;base64,{file_base64}"
                
                # Extract text using OCR
                extracted_text = await self.ocr_service.extract_text(document_url, doc_type)
                
                return extracted_text
                
            finally:
                # Clean up temporary file
                if os.path.exists(temp_file_path):
                    os.unlink(temp_file_path)
                    
        except Exception as e:
            logger.error(f"Error in OCR processing for {filename}: {str(e)}")
            return None
    
    async def get_service_status(self) -> Dict[str, Any]:
        """
        Get the status of all document processing services.
        
        Returns:
            Status information for OCR and entity extraction services
        """
        try:
            # Test OCR service
            ocr_status = await self.ocr_service.test_connection()
            
            # Test entity extraction service
            entity_status = await self.entity_service.test_connection()
            
            return {
                "ocr_service": {
                    "available": ocr_status,
                    "provider": "Mistral OCR",
                    "capabilities": [
                        "PDF text extraction",
                        "Image OCR",
                        "Multi-language support",
                        "Advanced document analysis"
                    ]
                },
                "entity_extraction": {
                    "available": entity_status,
                    "provider": "OpenRouter",
                    "model": "meta-llama/llama-3.1-8b-instruct:free",
                    "capabilities": [
                        "Medical entity extraction",
                        "Scientific concept identification",
                        "Relationship detection",
                        "Context-aware analysis"
                    ]
                },
                "pipeline_status": "ready" if (ocr_status and entity_status) else "partial",
                "supported_formats": [
                    "PDF (.pdf)",
                    "Text files (.txt, .md)",
                    "Word documents (.doc, .docx)",
                    "Images (.jpg, .png, .gif, .bmp)"
                ]
            }
            
        except Exception as e:
            logger.error(f"Error getting service status: {str(e)}")
            return {
                "error": str(e),
                "pipeline_status": "error"
            }
    
    async def extract_entities_from_text(self, text: str) -> List[Dict[str, Any]]:
        """
        Extract entities from plain text.
        
        Args:
            text: Input text
        
        Returns:
            List of extracted entities
        """
        try:
            return await self.entity_service.extract_entities(text)
        except Exception as e:
            logger.error(f"Error extracting entities from text: {str(e)}")
            return []
