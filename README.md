<p align="center">
  <a href="https://www.getzep.com/">
    <img src="https://github.com/user-attachments/assets/119c5682-9654-4257-8922-56b7cb8ffd73" width="150" alt="Zep Logo">
  </a>
</p>

<h1 align="center">
Graphiti
</h1>
<h2 align="center"> Build Real-Time Knowledge Graphs for AI Agents</h2>
<div align="center">

[![Lint](https://github.com/getzep/Graphiti/actions/workflows/lint.yml/badge.svg?style=flat)](https://github.com/getzep/Graphiti/actions/workflows/lint.yml)
[![Unit Tests](https://github.com/getzep/Graphiti/actions/workflows/unit_tests.yml/badge.svg)](https://github.com/getzep/Graphiti/actions/workflows/unit_tests.yml)
[![MyPy Check](https://github.com/getzep/Graphiti/actions/workflows/typecheck.yml/badge.svg)](https://github.com/getzep/Graphiti/actions/workflows/typecheck.yml)

![GitHub Repo stars](https://img.shields.io/github/stars/getzep/graphiti)
[![Discord](https://dcbadge.vercel.app/api/server/W8Kw6bsgXQ?style=flat)](https://discord.com/invite/W8Kw6bsgXQ)
[![arXiv](https://img.shields.io/badge/arXiv-2501.13956-b31b1b.svg?style=flat)](https://arxiv.org/abs/2501.13956)
[![Release](https://img.shields.io/github/v/release/getzep/graphiti?style=flat&label=Release&color=limegreen)](https://github.com/getzep/graphiti/releases)

</div>
<div align="center">

<a href="https://trendshift.io/repositories/12986" target="_blank"><img src="https://trendshift.io/api/badge/repositories/12986" alt="getzep%2Fgraphiti | Trendshift" style="width: 250px; height: 55px;" width="250" height="55"/></a>

</div>

:star: _Help us reach more developers and grow the Graphiti community. Star this repo!_

<br />

> [!TIP]
> Check out the new [MCP server for Graphiti](mcp_server/README.md)! Give Claude, Cursor, and other MCP clients powerful Knowledge Graph-based memory.

Graphiti is a framework for building and querying temporally-aware knowledge graphs, specifically tailored for AI agents operating in dynamic environments. Unlike traditional retrieval-augmented generation (RAG) methods, Graphiti continuously integrates user interactions, structured and unstructured enterprise data, and external information into a coherent, queryable graph. The framework supports incremental data updates, efficient retrieval, and precise historical queries without requiring complete graph recomputation, making it suitable for developing interactive, context-aware AI applications.

Use Graphiti to:

- Integrate and maintain dynamic user interactions and business data.
- Facilitate state-based reasoning and task automation for agents.
- Query complex, evolving data with semantic, keyword, and graph-based search methods.

<br />

<p align="center">
    <img src="images/graphiti-graph-intro.gif" alt="Graphiti temporal walkthrough" width="700px">   
</p>

<br />

A knowledge graph is a network of interconnected facts, such as _"Kendra loves Adidas shoes."_ Each fact is a "triplet" represented by two entities, or
nodes ("Kendra", "Adidas shoes"), and their relationship, or edge ("loves"). Knowledge Graphs have been explored
extensively for information retrieval. What makes Graphiti unique is its ability to autonomously build a knowledge graph
while handling changing relationships and maintaining historical context.

## Graphiti and Zep Memory

Graphiti powers the core of [Zep's memory layer](https://www.getzep.com) for AI Agents.

Using Graphiti, we've demonstrated Zep is
the [State of the Art in Agent Memory](https://blog.getzep.com/state-of-the-art-agent-memory/).

Read our paper: [Zep: A Temporal Knowledge Graph Architecture for Agent Memory](https://arxiv.org/abs/2501.13956).

We're excited to open-source Graphiti, believing its potential reaches far beyond AI memory applications.

<p align="center">
    <a href="https://arxiv.org/abs/2501.13956"><img src="images/arxiv-screenshot.png" alt="Zep: A Temporal Knowledge Graph Architecture for Agent Memory" width="700px"></a>
</p>

## Why Graphiti?

Traditional RAG approaches often rely on batch processing and static data summarization, making them inefficient for frequently changing data. Graphiti addresses these challenges by providing:

- **Real-Time Incremental Updates:** Immediate integration of new data episodes without batch recomputation.
- **Bi-Temporal Data Model:** Explicit tracking of event occurrence and ingestion times, allowing accurate point-in-time queries.
- **Efficient Hybrid Retrieval:** Combines semantic embeddings, keyword (BM25), and graph traversal to achieve low-latency queries without reliance on LLM summarization.
- **Custom Entity Definitions:** Flexible ontology creation and support for developer-defined entities through straightforward Pydantic models.
- **Scalability:** Efficiently manages large datasets with parallel processing, suitable for enterprise environments.

<p align="center">
    <img src="/images/graphiti-intro-slides-stock-2.gif" alt="Graphiti structured + unstructured demo" width="700px">   
</p>

## Graphiti vs. GraphRAG

| Aspect                     | GraphRAG                              | Graphiti                                         |
| -------------------------- | ------------------------------------- | ------------------------------------------------ |
| **Primary Use**            | Static document summarization         | Dynamic data management                          |
| **Data Handling**          | Batch-oriented processing             | Continuous, incremental updates                  |
| **Knowledge Structure**    | Entity clusters & community summaries | Episodic data, semantic entities, communities    |
| **Retrieval Method**       | Sequential LLM summarization          | Hybrid semantic, keyword, and graph-based search |
| **Adaptability**           | Low                                   | High                                             |
| **Temporal Handling**      | Basic timestamp tracking              | Explicit bi-temporal tracking                    |
| **Contradiction Handling** | LLM-driven summarization judgments    | Temporal edge invalidation                       |
| **Query Latency**          | Seconds to tens of seconds            | Typically sub-second latency                     |
| **Custom Entity Types**    | No                                    | Yes, customizable                                |
| **Scalability**            | Moderate                              | High, optimized for large datasets               |

Graphiti is specifically designed to address the challenges of dynamic and frequently updated datasets, making it particularly suitable for applications requiring real-time interaction and precise historical queries.

## Installation

Requirements:

- Python 3.10 or higher
- Neo4j 5.26 or higher (serves as the embeddings storage backend)
- OpenAI API key (for LLM inference and embedding)

> [!IMPORTANT]
> Graphiti works best with LLM services that support Structured Output (such as OpenAI and Gemini).
> Using other services may result in incorrect output schemas and ingestion failures. This is particularly
> problematic when using smaller models.

Optional:

- Google Gemini, Anthropic, or Groq API key (for alternative LLM providers)

> [!TIP]
> The simplest way to install Neo4j is via [Neo4j Desktop](https://neo4j.com/download/). It provides a user-friendly
> interface to manage Neo4j instances and databases.

```bash
pip install graphiti-core
```

or

```bash
poetry add graphiti-core
```

You can also install optional LLM providers as extras:

```bash
# Install with Anthropic support
pip install graphiti-core[anthropic]

# Install with Groq support
pip install graphiti-core[groq]

# Install with Google Gemini support
pip install graphiti-core[google-genai]

# Install with multiple providers
pip install graphiti-core[anthropic,groq,google-genai]
```

## Quick Start

> [!IMPORTANT]
> Graphiti uses OpenAI for LLM inference and embedding. Ensure that an `OPENAI_API_KEY` is set in your environment.
> Support for Anthropic and Groq LLM inferences is available, too. Other LLM providers may be supported via OpenAI
> compatible APIs.

For a complete working example, see the [Quickstart Example](./examples/quickstart/README.md) in the examples directory. The quickstart demonstrates:

1. Connecting to a Neo4j database
2. Initializing Graphiti indices and constraints
3. Adding episodes to the graph (both text and structured JSON)
4. Searching for relationships (edges) using hybrid search
5. Reranking search results using graph distance
6. Searching for nodes using predefined search recipes

The example is fully documented with clear explanations of each functionality and includes a comprehensive README with setup instructions and next steps.

## MCP Server

The `mcp_server` directory contains a Model Context Protocol (MCP) server implementation for Graphiti. This server allows AI assistants to interact with Graphiti's knowledge graph capabilities through the MCP protocol.

Key features of the MCP server include:

- Episode management (add, retrieve, delete)
- Entity management and relationship handling
- Semantic and hybrid search capabilities
- Group management for organizing related data
- Graph maintenance operations

The MCP server can be deployed using Docker with Neo4j, making it easy to integrate Graphiti into your AI assistant workflows.

For detailed setup instructions and usage examples, see the [MCP server README](./mcp_server/README.md).

## REST Service

The `server` directory contains an API service for interacting with the Graphiti API. It is built using FastAPI.

Please see the [server README](./server/README.md) for more information.

## Optional Environment Variables

In addition to the Neo4j and OpenAi-compatible credentials, Graphiti also has a few optional environment variables.
If you are using one of our supported models, such as Anthropic or Voyage models, the necessary environment variables
must be set.

`USE_PARALLEL_RUNTIME` is an optional boolean variable that can be set to true if you wish
to enable Neo4j's parallel runtime feature for several of our search queries.
Note that this feature is not supported for Neo4j Community edition or for smaller AuraDB instances,
as such this feature is off by default.

## Using Graphiti with Azure OpenAI

Graphiti supports Azure OpenAI for both LLM inference and embeddings. To use Azure OpenAI, you'll need to configure both the LLM client and embedder with your Azure OpenAI credentials.

```python
from openai import AsyncAzureOpenAI
from graphiti_core import Graphiti
from graphiti_core.llm_client import OpenAIClient
from graphiti_core.embedder.openai import OpenAIEmbedder, OpenAIEmbedderConfig
from graphiti_core.cross_encoder.openai_reranker_client import OpenAIRerankerClient

# Azure OpenAI configuration
api_key = "<your-api-key>"
api_version = "<your-api-version>"
azure_endpoint = "<your-azure-endpoint>"

# Create Azure OpenAI client for LLM
azure_openai_client = AsyncAzureOpenAI(
    api_key=api_key,
    api_version=api_version,
    azure_endpoint=azure_endpoint
)

# Initialize Graphiti with Azure OpenAI clients
graphiti = Graphiti(
    "bolt://localhost:7687",
    "neo4j",
    "password",
    llm_client=OpenAIClient(
        client=azure_openai_client
    ),
    embedder=OpenAIEmbedder(
        config=OpenAIEmbedderConfig(
            embedding_model="text-embedding-3-small"  # Use your Azure deployed embedding model name
        ),
        client=azure_openai_client
    ),
    # Optional: Configure the OpenAI cross encoder with Azure OpenAI
    cross_encoder=OpenAIRerankerClient(
        client=azure_openai_client
    )
)

# Now you can use Graphiti with Azure OpenAI
```

Make sure to replace the placeholder values with your actual Azure OpenAI credentials and specify the correct embedding model name that's deployed in your Azure OpenAI service.

## Using Graphiti with Google Gemini

Graphiti supports Google's Gemini models for both LLM inference and embeddings. To use Gemini, you'll need to configure both the LLM client and embedder with your Google API key.

Install Graphiti:

```bash
poetry add "graphiti-core[google-genai]"

# or

uv add "graphiti-core[google-genai]"
```

```python
from graphiti_core import Graphiti
from graphiti_core.llm_client.gemini_client import GeminiClient, LLMConfig
from graphiti_core.embedder.gemini import GeminiEmbedder, GeminiEmbedderConfig

# Google API key configuration
api_key = "<your-google-api-key>"

# Initialize Graphiti with Gemini clients
graphiti = Graphiti(
    "bolt://localhost:7687",
    "neo4j",
    "password",
    llm_client=GeminiClient(
        config=LLMConfig(
            api_key=api_key,
            model="gemini-2.0-flash"
        )
    ),
    embedder=GeminiEmbedder(
        config=GeminiEmbedderConfig(
            api_key=api_key,
            embedding_model="embedding-001"
        )
    )
)

# Now you can use Graphiti with Google Gemini
```

## Documentation

- [Guides and API documentation](https://help.getzep.com/graphiti).
- [Quick Start](https://help.getzep.com/graphiti/graphiti/quick-start)
- [Building an agent with LangChain's LangGraph and Graphiti](https://help.getzep.com/graphiti/graphiti/lang-graph-agent)

## Status and Roadmap

Graphiti is under active development. We aim to maintain API stability while working on:

- [x] Supporting custom graph schemas:
  - Allow developers to provide their own defined node and edge classes when ingesting episodes
  - Enable more flexible knowledge representation tailored to specific use cases
- [x] Enhancing retrieval capabilities with more robust and configurable options
- [x] Graphiti MCP Server
- [ ] Expanding test coverage to ensure reliability and catch edge cases

## Contributing

We encourage and appreciate all forms of contributions, whether it's code, documentation, addressing GitHub Issues, or
answering questions in the Graphiti Discord channel. For detailed guidelines on code contributions, please refer
to [CONTRIBUTING](CONTRIBUTING.md).

## Support

Join the [Zep Discord server](https://discord.com/invite/W8Kw6bsgXQ) and make your way to the **#Graphiti** channel!

## Additional Features

### 🎯 **Graphiti UI - Complete Web Interface**

A comprehensive React-based web interface for interacting with Graphiti knowledge graphs:

#### **Current Features (✅ Implemented)**
- **💬 Chat Interface**: Interactive AI chat with document upload capabilities
- **📄 Document Upload**: Drag & drop interface with progress tracking and file validation
- **📊 Dashboard**: Overview of graph statistics and system health
- **🔍 Search**: Advanced search functionality for nodes and relationships
- **🌐 Graph Visualization**: Interactive knowledge graph explorer
- **⚙️ Data Ingestion**: Manual data entry and management interface

#### **🚧 In Development**
- **Mistral OCR Integration**: PDF text extraction using Mistral OCR API
- **OpenRouter Entity Extraction**: Entity extraction using meta-llama/llama-4-maverick
- **Advanced Analytics**: Graph metrics and insights dashboard

### Document Ingestion Pipeline (Planned)

The system will provide a streamlined pipeline: **Mistral OCR → Entities → Relationships → Embeddings → References → CSV**
This will process documents through all stages:
- 🚧 **Mistral OCR**: Extract text from PDF using Mistral OCR API
- 🚧 **Entity Extraction**: Extract medical/scientific entities using OpenRouter + meta-llama/llama-4-maverick
- 🚧 **Relationship Processing**: Deduplicate and merge entities with confidence scores
- 🚧 **Embeddings**: Generate embeddings using configured embedding service
- 🚧 **Reference Extraction**: Extract references to CSV using Mistral OCR + regex patterns
- ✅ **Database Storage**: Store in Neo4j with proper relationships and tracking

### UI Architecture

The Graphiti UI is built with modern web technologies:

1. **Frontend**: React 18 + TypeScript + Material-UI
2. **Backend**: FastAPI with async support
3. **Database**: Neo4j for graph storage
4. **Containerization**: Docker Compose for easy deployment
5. **Development**: Hot reload and development containers

### Getting Started with Graphiti UI

1. **Prerequisites**:
   - Docker and Docker Compose
   - Neo4j database (can be started with Docker Compose)
   - OpenAI API key for LLM operations

2. **Quick Start**:
   ```bash
   # Clone the repository
   git clone https://github.com/getzep/graphiti.git
   cd graphiti

   # Start all services
   docker-compose up -d

   # Access the UI
   open http://localhost:3567
   ```

3. **Configuration**:
   - Frontend runs on port 3567
   - Backend API runs on port 8234
   - Neo4j runs on port 7474 (browser) and 7687 (bolt)

### Pipeline Components (Planned)

1. **PDF Processing**: Will use Mistral OCR as primary method
2. **Entity Extraction**: Will use OpenRouter LLM services
3. **Embedding Generation**: Configurable embedding services
4. **Reference Extraction**: Will use Mistral OCR + regex patterns
5. **Database Integration**: Uses Neo4j for graph operations

## Using the Application

### Uploading Documents

1. Navigate to the **Upload** tab
2. Drag and drop document files or click to select files
   - Supported formats include: PDF, TXT, DOCX, RTF, MD, HTML, ODT, EPUB, CSV, XLSX, PPT, PPTX, XML
3. Configure processing options:
   - **Chunk Size**: 1200 characters (recommended)
   - **Overlap**: 0 characters (recommended)
   - **Extract entities automatically**: Enabled by default
   - **Extract metadata**: Extracts document title, authors, publication date, etc.
   - **Extract references**: Identifies and extracts citations and references
4. Click "Upload" to process the documents
5. The system will automatically process the document through the entire pipeline

### Exploring the Knowledge Graph

1. Navigate to the **Knowledge Graph Explorer** tab
2. Browse the hierarchical taxonomy of entities
3. Explore relationships between entities with confidence scores
4. View entity attributes and properties

### Searching the Knowledge Graph

1. Navigate to the **Search** tab
2. Use the advanced search options:
   - Filter by entity type
   - Filter by relationship type
   - Set minimum confidence score
   - Search by keyword
3. View search results with entity and relationship details

### Exploring Entities

1. Navigate to the **Entities** tab
2. Browse entities by type or search for specific entities
3. Click on an entity to view details, including:
   - Entity type
   - Description
   - Domain-specific attributes
   - Mentions in documents
   - Relationships to other entities with confidence scores

### Asking Questions

1. Navigate to the **Answer Questions** tab
2. Type your question in the input field
3. View the evidence-based answer with numbered references
4. Click on references to see the source information
5. References are displayed with:
   - Sequential numbering that matches the reference numbers in the answer
   - Proper document titles and identifiers
   - Formatted content with support for mathematical notation and special characters
   - Metadata including author, year, and journal information when available
6. Continue the conversation with follow-up questions
7. Clear the conversation history when needed

## Document Processing Workflow

1. **Document Upload**: Various document types are uploaded to the system
2. **Document Type Detection**: The system automatically detects the file format
3. **Text Extraction**: Text is extracted using the appropriate parser:
   - PDF files: Mistral OCR or PyPDF2
   - DOCX/DOC: Document parser
   - TXT/MD/RTF: Text parser
   - HTML: HTML parser
   - And other format-specific parsers
4. **Metadata Extraction**: Document metadata (title, authors, date, etc.) is extracted
5. **Reference Extraction**: Citations and references are identified and extracted
   - Scientific references are extracted using LLM and regex patterns
   - **References are stored separately from the graph database** in CSV and JSON formats
   - Bibliographic information is captured (authors, title, journal, year, etc.)
   - References are deduplicated and organized by source document
   - **References are maintained as a separate pipeline component** for later use
6. **Chunking**: Text is divided into manageable chunks (facts) of 1200 characters
7. **Storage**: Chunks are stored as Fact nodes linked to Episode (document) nodes
8. **Entity Extraction**: Entities are identified in the facts and stored as Entity nodes with types
9. **Relationship Extraction**: Relationships between entities are identified with confidence scores
10. **Hierarchical Categorization**: Entities are organized in a taxonomic structure
11. **Attribute Extraction**: Domain-specific attributes are added to entities
12. **Vector Embedding**: Text chunks are embedded for semantic search
13. **Knowledge Graph Integration**: All components are connected in the FalkorDB database

### Reference Architecture

**Important**: References are handled as a **separate state/pipeline** from the main knowledge graph:

- **Storage**: References are stored in CSV files and accessed via the `/api/references` endpoint
- **Purpose**: References are maintained for future use and citation tracking
- **Linking**: References can be traced back to their original documents in the graph database
- **Independence**: The reference system operates independently of the graph database
- **Access**: References are displayed in the UI through the References tab and used for Q&A citations

### Graphiti Process Flow Diagram

```mermaid
graph TD
    %% Main Components
    User([User])
    WebUI[Web Interface]
    DocumentProcessor[Document Processor]
    FormatDetector[Format Detector]
    PDFProcessor[PDF Processor]
    DocProcessor[Document Parser]
    TextProcessor[Text Parser]
    HTMLProcessor[HTML Parser]
    MetadataExtractor[Metadata Extractor]
    ReferenceExtractor[Reference Extractor]
    GraphitiCore[Graphiti Core]
    FalkorDB[(FalkorDB Database)]
    LLM[LLM Service]
    Embedder[Embedding Service]
    RelationshipExtractor[Relationship Extractor]
    AttributeExtractor[Attribute Extractor]
    TaxonomyBuilder[Taxonomy Builder]

    %% Document Processing Flow
    User -->|Upload Document| WebUI
    WebUI -->|Process Document| DocumentProcessor
    DocumentProcessor -->|Detect Format| FormatDetector
    FormatDetector -->|PDF| PDFProcessor
    FormatDetector -->|DOCX/DOC| DocProcessor
    FormatDetector -->|TXT/MD/RTF| TextProcessor
    FormatDetector -->|HTML| HTMLProcessor

    PDFProcessor -->|Extract Text| MistralOCR{Mistral OCR Available?}
    MistralOCR -->|Yes| MistralProcessor[Mistral OCR Processor]
    MistralOCR -->|No| PyPDF[PyPDF2 Processor]
    MistralProcessor -->|Return Text| TextChunking[Text Chunking]
    PyPDF -->|Return Text| TextChunking

    DocProcessor -->|Extract Text| TextChunking
    TextProcessor -->|Extract Text| TextChunking
    HTMLProcessor -->|Extract Text| TextChunking

    DocumentProcessor -->|Extract Metadata| MetadataExtractor
    DocumentProcessor -->|Extract References| ReferenceExtractor
    ReferenceExtractor -->|Scientific References| ReferenceStorage[Reference Storage]
    ReferenceStorage -->|CSV Format| CSVReferences[(CSV References)]
    ReferenceStorage -->|JSON Format| JSONReferences[(JSON References)]

    %% Knowledge Graph Building
    TextChunking -->|Chunked Text| GraphitiCore
    MetadataExtractor -->|Document Metadata| GraphitiCore
    ReferenceExtractor -->|Citations & References| GraphitiCore
    GraphitiCore -->|Create Episode Node| FalkorDB
    GraphitiCore -->|Create Fact Nodes| FalkorDB
    GraphitiCore -->|Extract Entities| LLM
    LLM -->|Entity List| GraphitiCore
    GraphitiCore -->|Create Entity Nodes| FalkorDB
    GraphitiCore -->|Extract Relationships| RelationshipExtractor
    RelationshipExtractor -->|Relationships with Confidence| GraphitiCore
    GraphitiCore -->|Create Relationships| FalkorDB
    GraphitiCore -->|Build Taxonomy| TaxonomyBuilder
    TaxonomyBuilder -->|Hierarchical Structure| GraphitiCore
    GraphitiCore -->|Extract Attributes| AttributeExtractor
    AttributeExtractor -->|Domain-Specific Attributes| GraphitiCore
    GraphitiCore -->|Update Entity Attributes| FalkorDB
    GraphitiCore -->|Generate Embeddings| Embedder
    Embedder -->|Vector Embeddings| GraphitiCore
    GraphitiCore -->|Store Embeddings| FalkorDB

    %% Querying Flow
    User -->|Ask Question| WebUI
    WebUI -->|Query| GraphitiCore
    GraphitiCore -->|Vector Search| FalkorDB
    FalkorDB -->|Relevant Facts & Entities| GraphitiCore
    GraphitiCore -->|Generate Answer| LLM
    LLM -->|Answer with References| GraphitiCore
    GraphitiCore -->|Display Answer| WebUI
    WebUI -->|Show Answer| User

    %% Advanced Search Flow
    User -->|Advanced Search| WebUI
    WebUI -->|Search Parameters| GraphitiCore
    GraphitiCore -->|Filtered Query| FalkorDB
    FalkorDB -->|Search Results| GraphitiCore
    GraphitiCore -->|Format Results| WebUI
    WebUI -->|Display Results| User

    %% Knowledge Graph Exploration
    User -->|Explore Knowledge Graph| WebUI
    WebUI -->|Get Taxonomy| GraphitiCore
    GraphitiCore -->|Query Taxonomy| FalkorDB
    FalkorDB -->|Hierarchical Data| GraphitiCore
    GraphitiCore -->|Taxonomy Information| WebUI
    WebUI -->|Display Knowledge Graph| User

    %% Styling
    classDef primary fill:#f9f,stroke:#333,stroke-width:2px;
    classDef secondary fill:#bbf,stroke:#333,stroke-width:1px;
    classDef database fill:#bfb,stroke:#333,stroke-width:1px;
    classDef service fill:#fbb,stroke:#333,stroke-width:1px;
    classDef decision fill:#fffacd,stroke:#333,stroke-width:1px;
    classDef extractor fill:#ffd,stroke:#333,stroke-width:1px;

    class WebUI,GraphitiCore primary;
    class DocumentProcessor,FormatDetector,PDFProcessor,DocProcessor,TextProcessor,HTMLProcessor,TextChunking,MistralProcessor,PyPDF secondary;
    class FalkorDB,CSVReferences,JSONReferences database;
    class LLM,Embedder,MistralOCR service;
    class MistralOCR,FormatDetector decision;
    class MetadataExtractor,ReferenceExtractor,RelationshipExtractor,AttributeExtractor,TaxonomyBuilder extractor;
    class ReferenceStorage extractor;
```

## Entity Types

The system currently recognizes and categorizes entities into 19+ types including:

- **Herb**: Medicinal plants and botanical remedies
- **Nutrient**: Vitamins, minerals, and other nutritional compounds
- **Disease**: Medical conditions and disorders
- **Medication**: Pharmaceutical drugs and treatments
- **Symptom**: Clinical manifestations of conditions
- **Process**: Biological or chemical processes
- **Treatment**: Therapeutic approaches and interventions
- **Research**: Studies, trials, and research methodologies
- **Organization**: Research institutions, companies, and groups
- **Person**: Researchers, authors, and individuals
- **Food**: Dietary items and food groups
- **Concept**: Abstract ideas and theoretical constructs
- **Location**: Geographical places and regions
- **Chemical**: Chemical compounds and substances
- **Protein**: Protein molecules and structures
- **Plant**: Plant species and botanical classifications
- **Ingredient**: Components of formulations
- **Hormone**: Endocrine signaling molecules
- **Study**: Specific research studies and clinical trials

## Relationship Types

The system supports 20+ relationship types with confidence scores:

- **IS_A**: Hierarchical classification (e.g., "Vitamin C IS_A Nutrient")
- **PART_OF**: Component relationships (e.g., "Flavonoids PART_OF Green Tea")
- **TREATS**: Therapeutic relationships (e.g., "Echinacea TREATS Common Cold")
- **CAUSES**: Causal relationships (e.g., "Smoking CAUSES Lung Cancer")
- **PREVENTS**: Preventative relationships (e.g., "Vitamin D PREVENTS Rickets")
- **CONTAINS**: Compositional relationships (e.g., "Milk Thistle CONTAINS Silymarin")
- **INTERACTS_WITH**: Interaction relationships (e.g., "St. John's Wort INTERACTS_WITH Warfarin")
- **CONTRAINDICATES**: Contraindication relationships (e.g., "Ginkgo Biloba CONTRAINDICATES Blood Thinners")
- **INCREASES**: Enhancement relationships (e.g., "Exercise INCREASES Metabolism")
- **DECREASES**: Reduction relationships (e.g., "Meditation DECREASES Stress")
- **NEEDS**: Requirement relationships (e.g., "Bone Formation NEEDS Vitamin D")
- **INHIBITS**: Suppression relationships (e.g., "Curcumin INHIBITS Inflammation")
- **ACTIVATES**: Stimulation relationships (e.g., "Sunlight ACTIVATES Vitamin D Production")
- **REGULATES**: Control relationships (e.g., "Insulin REGULATES Blood Sugar")
- **CONVERTS_TO**: Transformation relationships (e.g., "Beta Carotene CONVERTS_TO Vitamin A")
- **DERIVED_FROM**: Source relationships (e.g., "Resveratrol DERIVED_FROM Grapes")
- **USED_FOR**: Purpose relationships (e.g., "Aloe Vera USED_FOR Skin Healing")
- **MEASURED_BY**: Measurement relationships (e.g., "Vitamin B12 Status MEASURED_BY Serum Homocysteine")
- **ASSOCIATED_WITH**: General associations (e.g., "Turmeric ASSOCIATED_WITH Anti-inflammatory Properties")
- **STUDIED_BY**: Research relationships (e.g., "Ginseng STUDIED_BY Dr. Smith")

## Domain-Specific Attributes

Entities in the knowledge graph include domain-specific attributes that provide detailed information:

### Herb Attributes
- **active_compounds**: Chemical constituents with medicinal properties
- **traditional_uses**: Historical and traditional applications
- **dosage_range**: Recommended dosage information
- **contraindications**: Conditions where use is not advised
- **side_effects**: Potential adverse effects
- **interactions**: Interactions with medications or other substances
- **preparation_methods**: Methods of preparation and administration

### Nutrient Attributes
- **food_sources**: Natural sources of the nutrient
- **daily_requirements**: Recommended daily intake
- **deficiency_symptoms**: Signs of deficiency
- **toxicity_symptoms**: Signs of excessive intake
- **functions**: Biological functions and roles
- **absorption_factors**: Factors affecting absorption

## Advanced Search Capabilities

The advanced search functionality allows for sophisticated queries:

1. **Entity Type Filtering**: Search for specific types of entities
2. **Relationship Type Filtering**: Find specific types of relationships
3. **Confidence Score Filtering**: Filter by minimum confidence level
4. **Keyword Search**: Search across entity names and descriptions
5. **Combined Filtering**: Combine multiple filters for precise results
6. **Result Categorization**: View results categorized by entity and relationship types

## Troubleshooting

If you encounter issues with the system, check the following:

1. **Entity Extraction Issues**:
   - Ensure the entity extraction process has completed (it runs in the background)
   - Verify that the document contains recognizable entities
   - Check that the LLM service is properly configured and accessible

2. **Database Connection Issues**:
   - Verify that the FalkorDB database is running and accessible
   - Check the connection parameters in the .env file
   - Ensure the database has sufficient storage space

3. **OCR Issues**:
   - If using Mistral OCR, verify that the API key is valid
   - For scanned documents, ensure they are of sufficient quality
   - Check the OCR provider configuration in the .env file

4. **Performance Issues**:
   - For large documents, increase available memory
   - Consider processing documents in smaller batches
   - Optimize FalkorDB configuration for better performance

## Recent Enhancements

The following enhancements have been implemented:

1. **FalkorDB Migration**: Migrated from Neo4j to FalkorDB for improved performance and scalability
2. **Vector Embeddings Integration**:
   - Added automatic generation of vector embeddings during document processing
   - Implemented 1024-dimensional embeddings using Ollama's snowflake-arctic-embed2 model
   - Created vector similarity search functionality for semantic search
   - ✅ **CONFIRMED WORKING**: All 51 embeddings successfully stored in Redis Vector Search
3. **Hybrid Search System**:
   - Combined traditional graph-based search with vector similarity search
   - Implemented manual cosine similarity calculation for FalkorDB compatibility
   - Added related entity retrieval for comprehensive search results
4. **Expanded Document Type Support**: Added support for multiple document formats beyond PDFs, including TXT, DOCX, RTF, HTML, and more
5. **Hierarchical Categorization**: Implemented IS_A and PART_OF relationships for structured taxonomy
6. **Relationship Extraction with Confidence Scores**: Added 20+ relationship types with confidence scoring
7. **Domain-Specific Attributes**: Added detailed attributes for different entity types
8. **Enhanced Web Interface**: Improved visualization and exploration of the knowledge graph
9. **Advanced Search**: Implemented sophisticated search capabilities with multiple filters
10. **🚀 ENHANCED MISTRAL OCR REFERENCE EXTRACTION** (Latest Update - May 26, 2025):
    - ✅ **FIXED**: Mistral OCR package import issues resolved for current mistralai package (v1.7.0)
    - ✅ **DRAMATICALLY IMPROVED**: Reference extraction went from 0 to 41+ references per document
    - ✅ **ENHANCED**: Advanced OCR processing for better text extraction from PDFs and presentations
    - ✅ **WORKING**: Mistral OCR successfully initializes and processes documents
    - Added extraction of citations and references from scientific documents
    - Implemented storage in structured formats (CSV and JSON)
    - Created API endpoints for accessing and downloading references
    - Developed deduplication system for references
    - Added batch processing for reference extraction from multiple documents
    - Enhanced reference extraction with support for different formats (numbered, author-year, bullet point)
    - Improved false positive detection to avoid capturing document content as references
    - Fixed character encoding issues in CSV export
11. **Metadata Extraction**: Implemented extraction of document metadata (title, authors, date, etc.)
12. **Parallel Document Processing**: Added support for processing multiple documents in parallel with configurable batch size
13. **Worker System for Document Processing**:
    - Implemented a modular worker system for parallel document processing
    - Created specialized worker types for different processing stages (document processing, entity extraction, reference extraction, embedding generation, database writing)
    - Added configurable worker counts for each processing stage
    - Implemented task queues for efficient workload distribution
    - Added monitoring and status tracking for processing tasks
    - Created API endpoints for worker management and monitoring
    - Implemented background processing for document uploads
14. **Enhanced Q&A Functionality**:
    - Improved source citation display with proper formatting
    - Implemented sequential numbering for sources that matches reference numbers in answers
    - Added support for mathematical notation and special characters
    - Improved document title display for better source identification
    - Fixed conversation history management for continuous interactions
15. **Settings Management**:
    - Added comprehensive settings management interface
    - Implemented API endpoints for updating settings
    - Added support for testing database connections
    - Created reset functionality for default settings
16. **Reference System Architecture** (Latest Update - May 30, 2025):
    - ✅ **CLARIFIED**: References are maintained as a separate pipeline component from the graph database
    - ✅ **FIXED**: Document dropdown in References tab now shows all documents with references
    - ✅ **IMPROVED**: Increased document fetch limit from 10 to 100 to include all documents
    - ✅ **RESOLVED**: Cocoa documents and other documents beyond the first 10 now appear in dropdown
    - References are stored in CSV/JSON format and accessed via `/api/references` endpoint
    - Reference system operates independently but can be linked back to original documents
    - Fixed JavaScript logic to properly match documents with their references

## Project Structure

```
graphiti/
├── .env                  # Environment variables
├── README.md            # Project documentation
├── TODO.md              # To-do list
├── app.py               # Main application file
├── config/              # Configuration files
│   └── workers.yaml     # Worker system configuration
├── database/            # Database access
│   ├── __init__.py
│   ├── falkordb_adapter.py
│   └── database_service.py
├── entity_extraction/   # Entity extraction package
│   ├── __init__.py
│   ├── base.py
│   ├── extractors/
│   │   ├── __init__.py
│   │   ├── llm_extractor.py
│   │   ├── rule_based_extractor.py
│   │   └── hybrid_extractor.py
│   ├── processors/
│   │   ├── __init__.py
│   │   ├── text_processor.py
│   │   └── entity_processor.py
│   ├── models/
│   │   ├── __init__.py
│   │   ├── entity.py
│   │   └── relationship.py
│   └── utils/
│       ├── __init__.py
│       ├── text_utils.py
│       └── validation.py
├── graphiti_core/       # Core functionality
│   ├── __init__.py
│   ├── core.py
│   ├── document/
│   │   ├── __init__.py
│   │   ├── document_processor.py
│   │   └── document_manager.py
│   ├── entity/
│   │   ├── __init__.py
│   │   ├── entity_processor.py
│   │   └── entity_manager.py
│   ├── knowledge_graph/
│   │   ├── __init__.py
│   │   ├── graph_builder.py
│   │   └── graph_manager.py
│   └── search/
│       ├── __init__.py
│       ├── search_engine.py
│       └── query_builder.py
├── models/              # Data models
│   ├── __init__.py
│   ├── document.py
│   ├── entity.py
│   ├── reference.py
│   └── knowledge_graph.py
├── reference_extraction/ # Reference extraction package
│   ├── __init__.py
│   ├── extractors/
│   │   ├── __init__.py
│   │   ├── citation_extractor.py
│   │   ├── bibliography_extractor.py
│   │   └── doi_extractor.py
│   ├── processors/
│   │   ├── __init__.py
│   │   ├── text_processor.py
│   │   └── reference_processor.py
│   ├── models/
│   │   ├── __init__.py
│   │   └── reference.py
│   └── utils/
│       ├── __init__.py
│       ├── formatting.py
│       └── validation.py
├── routes/              # API routes
│   ├── __init__.py
│   ├── document_routes.py
│   ├── entity_routes.py
│   ├── knowledge_graph_routes.py
│   ├── qa_routes.py
│   ├── reference_routes.py
│   ├── search_routes.py
│   ├── semantic_search_routes.py
│   ├── settings_routes.py
│   └── worker_routes.py
├── scripts/             # Utility scripts
│   ├── batch_process_pdfs.py
│   ├── check_database.py
│   ├── deduplicate_references.py
│   ├── extract_references_to_csv.py
│   ├── parallel_document_processor.py
│   ├── parallel_worker_processor.py
│   ├── process_all_pdfs.py
│   ├── process_references.py
│   ├── run_pdf_processor.py
│   ├── setup_falkordb.py
│   ├── setup_relationship_types.py
│   └── vector_search.py
├── services/            # Business logic
│   ├── __init__.py
│   ├── document_processing/
│   │   ├── __init__.py
│   │   ├── base_processor.py
│   │   ├── pdf_processor.py
│   │   ├── text_processor.py
│   │   ├── docx_processor.py
│   │   ├── html_processor.py
│   │   └── metadata_processor.py
│   ├── document_service.py
│   └── reference_service.py
├── static/              # Static files for web interface
│   ├── add_enhancements_link.js
│   ├── documents.js
│   ├── entities.js
│   ├── flask_conversation.js
│   ├── flask_entities.js
│   ├── flask_enhancements.js
│   ├── flask_graph.js
│   ├── flask_metadata.js
│   ├── flask_references.js
│   ├── flask_search.js
│   ├── flask_settings.js
│   ├── flask_upload.js
│   ├── graph_visualization.js
│   ├── hardcoded_answers.js
│   ├── metadata.js
│   ├── references.js
│   ├── search.js
│   ├── settings.js
│   ├── upload.js
│   └── web_interface_enhancements/
│       └── enhanced_visualizations.js
├── templates/           # HTML templates
│   └── index.html
├── tests/               # Unit and integration tests
│   ├── __init__.py
│   ├── conftest.py
│   ├── test_api_documents.py
│   ├── test_api_entities.py
│   ├── test_api_knowledge_graph.py
│   ├── test_api_qa.py
│   ├── test_api_references.py
│   ├── test_api_search.py
│   ├── test_api_settings.py
│   ├── test_file_utils.py
│   ├── test_settings.py
│   └── test_text_utils.py
├── utils/               # Utility functions
│   ├── __init__.py
│   ├── config.py
│   ├── dependencies.py
│   ├── embedding_utils.py
│   ├── entity_extraction_client.py
│   ├── error_handling.py
│   ├── file_utils.py
│   ├── local_llm_client.py
│   ├── logging_utils.py
│   ├── mistral_ocr.py
│   ├── open_router_client.py
│   └── text_utils.py
└── workers/             # Worker system for parallel processing
    ├── __init__.py
    ├── base.py
    ├── manager.py
    ├── monitoring/
    │   ├── __init__.py
    │   ├── performance_monitor.py
    │   └── status_tracker.py
    ├── processors/
    │   ├── __init__.py
    │   ├── database_processor.py
    │   ├── document_processor.py
    │   ├── embedding_processor.py
    │   ├── entity_processor.py
    │   └── reference_processor.py
    ├── queue/
    │   ├── __init__.py
    │   ├── priority_queue.py
    │   └── task_queue.py
    ├── README.md
    └── tasks/
        ├── __init__.py
        ├── document_tasks.py
        ├── entity_tasks.py
        ├── reference_tasks.py
        └── task_base.py
```