# Graphiti Full Stack Setup Guide

This guide will help you set up the complete Graphiti application with Neo4j database, FastAPI backend, and React frontend.

## 🏗️ Architecture Overview

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   React Frontend │    │  FastAPI Backend │    │   Neo4j Database │
│   (Port 3567)   │◄──►│   (Port 8234)   │◄──►│   (Port 7891)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 📋 Prerequisites

- **Docker Desktop** (with <PERSON>er Compose)
- **Git** (for cloning the repository)
- **OpenAI API Key** (for LLM functionality)

## 🚀 Quick Start

### 1. Environment Setup

The `.env` file has been configured with the following settings:

```env
# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key

# Neo4j Database Configuration
NEO4J_URI=bolt://localhost:7891
NEO4J_PORT=7891
NEO4J_USER=neo4j
NEO4J_PASSWORD=Triathlon16
DEFAULT_DATABASE=neo4j

# Frontend Configuration
REACT_APP_API_URL=http://localhost:8234
REACT_APP_NEO4J_BROWSER_URL=http://localhost:7892
```

### 2. Run Setup Script

**For Windows (PowerShell):**
```powershell
.\setup.ps1
```

**For macOS/Linux:**
```bash
chmod +x setup.sh
./setup.sh
```

### 3. Manual Setup (Alternative)

If you prefer to run commands manually:

```bash
# Build and start all services
docker-compose down -v
docker-compose build --no-cache
docker-compose up -d

# View logs
docker-compose logs -f
```

## 🌐 Service URLs

After setup, access your services at:

- **Frontend Application**: http://localhost:3567
- **Backend API**: http://localhost:8234
- **API Documentation**: http://localhost:8234/docs
- **Neo4j Browser**: http://localhost:7892

## 🔐 Neo4j Database Access

- **URL**: http://localhost:7892
- **Username**: `neo4j`
- **Password**: `Triathlon16`
- **Database**: `neo4j`

## 🎯 Features

### Frontend Features
- **Dashboard**: Overview of graph statistics
- **Graph Visualization**: Interactive graph display (placeholder for now)
- **Data Ingestion**: Add messages and entity nodes
- **Search**: Search nodes and facts in the knowledge graph

### Backend API Endpoints
- `GET /healthcheck` - Health check
- `POST /messages` - Add messages to the graph
- `POST /entity-node` - Add entity nodes
- `POST /search/nodes` - Search for nodes
- `POST /search/facts` - Search for facts/relationships
- `GET /episodes/{group_id}` - Get episodes for a group
- `DELETE /entity-edge/{uuid}` - Delete entity edge
- `DELETE /group/{group_id}` - Delete group
- `DELETE /episode/{uuid}` - Delete episode
- `POST /clear` - Clear the entire graph

## 🛠️ Development

### Frontend Development
```bash
cd frontend
npm install
npm start
```

### Backend Development
```bash
cd server
poetry install
poetry run uvicorn graph_service.main:app --reload
```

### Database Management
```bash
# View Neo4j logs
docker-compose logs neo4j

# Reset database
docker-compose down -v
docker-compose up neo4j -d
```

## 🔧 Useful Commands

```bash
# View all service logs
docker-compose logs -f

# View specific service logs
docker-compose logs -f frontend
docker-compose logs -f graph
docker-compose logs -f neo4j

# Stop all services
docker-compose down

# Stop and remove volumes (reset database)
docker-compose down -v

# Restart specific service
docker-compose restart frontend

# Rebuild and restart
docker-compose build --no-cache
docker-compose up -d
```

## 🐛 Troubleshooting

### Common Issues

1. **Port conflicts**: Make sure ports 3000, 7474, 7687, and 8000 are available
2. **Docker issues**: Restart Docker Desktop
3. **Neo4j connection**: Wait 30-60 seconds for Neo4j to fully start
4. **Frontend not loading**: Check if backend is running and accessible

### Health Checks

```bash
# Check if services are running
curl http://localhost:8234/healthcheck
curl http://localhost:3567
curl http://localhost:7892
```

### Reset Everything

```bash
docker-compose down -v
docker system prune -f
docker-compose build --no-cache
docker-compose up -d
```

## 📚 Next Steps

1. **Add Graph Visualization**: Integrate vis.js, d3.js, or cytoscape.js
2. **Enhance Search**: Add advanced filtering and sorting
3. **Add Authentication**: Implement user management
4. **Real-time Updates**: Add WebSocket support
5. **Export Features**: Add data export functionality
6. **Monitoring**: Add logging and metrics

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is licensed under the Apache 2.0 License.
