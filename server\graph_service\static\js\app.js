// Graphiti Flask Frontend JavaScript

// API Base URL
const API_BASE = '';

// Global state
let currentTab = 'dashboard';
let chatHistory = [];

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    initializeNavigation();
    initializeForms();
    loadDashboardStats();
    
    // Show dashboard by default
    showTab('dashboard');
});

// Navigation handling
function initializeNavigation() {
    const navLinks = document.querySelectorAll('[data-tab]');
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const tabName = this.getAttribute('data-tab');
            showTab(tabName);
            
            // Update active nav link
            navLinks.forEach(l => l.classList.remove('active'));
            this.classList.add('active');
        });
    });
}

// Tab switching
function showTab(tabName) {
    // Hide all tabs
    const tabs = document.querySelectorAll('.tab-content');
    tabs.forEach(tab => tab.classList.remove('active'));
    
    // Show selected tab
    const selectedTab = document.getElementById(`${tabName}-tab`);
    if (selectedTab) {
        selectedTab.classList.add('active');
        currentTab = tabName;
        
        // Load tab-specific data
        switch(tabName) {
            case 'dashboard':
                loadDashboardStats();
                break;
            case 'search':
                // Initialize search if needed
                break;
            case 'graph':
                // Initialize graph if needed
                break;
            case 'chat':
                // Initialize chat if needed
                break;
        }
    }
}

// Form initialization
function initializeForms() {
    // Upload form
    const uploadForm = document.getElementById('upload-form');
    if (uploadForm) {
        uploadForm.addEventListener('submit', handleFileUpload);
    }
    
    // Search form
    const searchForm = document.getElementById('search-form');
    if (searchForm) {
        searchForm.addEventListener('submit', handleSearch);
    }
    
    // Chat form
    const chatForm = document.getElementById('chat-form');
    if (chatForm) {
        chatForm.addEventListener('submit', handleChatMessage);
    }
}

// Dashboard functions
async function loadDashboardStats() {
    try {
        // For now, we'll use mock data since the specific stats endpoints might not exist
        // In a real implementation, you would call actual API endpoints
        updateDashboardStats({
            totalNodes: 0,
            totalEdges: 0,
            totalGroups: 1,
            recentEpisodes: 0
        });
    } catch (error) {
        console.error('Error loading dashboard stats:', error);
        showAlert('Error loading dashboard statistics', 'danger');
    }
}

function updateDashboardStats(stats) {
    document.getElementById('total-nodes').textContent = stats.totalNodes || 0;
    document.getElementById('total-edges').textContent = stats.totalEdges || 0;
    document.getElementById('total-groups').textContent = stats.totalGroups || 0;
    document.getElementById('recent-episodes').textContent = stats.recentEpisodes || 0;
}

// File upload handling
async function handleFileUpload(e) {
    e.preventDefault();
    
    const fileInput = document.getElementById('file-input');
    const groupId = document.getElementById('group-id').value;
    const uploadType = document.getElementById('upload-type').value;
    
    if (!fileInput.files[0]) {
        showAlert('Please select a file to upload', 'warning');
        return;
    }
    
    const formData = new FormData();
    formData.append('file', fileInput.files[0]);
    formData.append('group_id', groupId);
    formData.append('upload_type', uploadType);
    
    try {
        showUploadProgress(true);
        updateUploadStatus('Uploading file...');
        
        const response = await fetch('/api/documents/upload', {
            method: 'POST',
            body: formData
        });
        
        if (response.ok) {
            const result = await response.json();
            showAlert('File uploaded successfully!', 'success');
            updateUploadStatus('Upload completed successfully');
            
            // Reset form
            fileInput.value = '';
            
            // Refresh dashboard stats
            loadDashboardStats();
        } else {
            const error = await response.json();
            throw new Error(error.detail || 'Upload failed');
        }
    } catch (error) {
        console.error('Upload error:', error);
        showAlert(`Upload failed: ${error.message}`, 'danger');
        updateUploadStatus('Upload failed');
    } finally {
        showUploadProgress(false);
    }
}

function showUploadProgress(show) {
    const progressDiv = document.getElementById('upload-progress');
    if (progressDiv) {
        progressDiv.style.display = show ? 'block' : 'none';
        if (show) {
            // Simulate progress
            const progressBar = progressDiv.querySelector('.progress-bar');
            let progress = 0;
            const interval = setInterval(() => {
                progress += 10;
                progressBar.style.width = `${progress}%`;
                if (progress >= 100) {
                    clearInterval(interval);
                }
            }, 200);
        }
    }
}

function updateUploadStatus(message) {
    const statusDiv = document.getElementById('upload-status');
    if (statusDiv) {
        statusDiv.textContent = message;
    }
}

// Search handling
async function handleSearch(e) {
    e.preventDefault();
    
    const query = document.getElementById('search-query').value;
    const groupId = document.getElementById('search-group').value;
    const searchType = document.getElementById('search-type').value;
    const maxResults = parseInt(document.getElementById('max-results').value);
    
    if (!query.trim()) {
        showAlert('Please enter a search query', 'warning');
        return;
    }
    
    try {
        const endpoint = searchType === 'nodes' ? '/search/nodes' : '/search/facts';
        const requestData = {
            query: query,
            group_ids: [groupId],
            ...(searchType === 'facts' ? { max_facts: maxResults } : { limit: maxResults })
        };
        
        const response = await fetch(endpoint, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestData)
        });
        
        if (response.ok) {
            const results = await response.json();
            displaySearchResults(results, searchType);
        } else {
            throw new Error('Search failed');
        }
    } catch (error) {
        console.error('Search error:', error);
        showAlert(`Search failed: ${error.message}`, 'danger');
    }
}

function displaySearchResults(results, searchType) {
    const resultsDiv = document.getElementById('search-results');
    
    if (!results || results.length === 0) {
        resultsDiv.innerHTML = '<div class="text-muted">No results found</div>';
        return;
    }
    
    let html = `<h6>Found ${results.length} ${searchType}</h6>`;
    
    results.forEach((result, index) => {
        html += `
            <div class="search-result-item fade-in">
                <div class="search-result-title">
                    ${searchType === 'nodes' ? (result.name || `Node ${index + 1}`) : (result.fact || `Fact ${index + 1}`)}
                </div>
                <div class="search-result-content">
                    ${result.summary || result.content || 'No description available'}
                </div>
                <div class="search-result-meta">
                    ${result.uuid ? `ID: ${result.uuid.substring(0, 8)}...` : ''}
                    ${result.created_at ? ` | Created: ${new Date(result.created_at).toLocaleDateString()}` : ''}
                </div>
            </div>
        `;
    });
    
    resultsDiv.innerHTML = html;
}

// Chat handling
async function handleChatMessage(e) {
    e.preventDefault();
    
    const input = document.getElementById('chat-input');
    const message = input.value.trim();
    
    if (!message) return;
    
    // Add user message to chat
    addChatMessage(message, 'user');
    input.value = '';
    
    try {
        // For now, we'll add a simple response
        // In a real implementation, you would call a chat/QA API endpoint
        setTimeout(() => {
            addChatMessage('I\'m a placeholder response. Chat functionality will be implemented with the actual API endpoints.', 'assistant');
        }, 1000);
        
    } catch (error) {
        console.error('Chat error:', error);
        addChatMessage('Sorry, I encountered an error processing your message.', 'assistant');
    }
}

function addChatMessage(content, sender) {
    const messagesDiv = document.getElementById('chat-messages');
    const messageDiv = document.createElement('div');
    messageDiv.className = `chat-message ${sender}`;
    
    const now = new Date().toLocaleTimeString();
    messageDiv.innerHTML = `
        <div class="chat-message-content">${content}</div>
        <div class="chat-message-time">${now}</div>
    `;
    
    messagesDiv.appendChild(messageDiv);
    messagesDiv.scrollTop = messagesDiv.scrollHeight;
    
    // Store in history
    chatHistory.push({ content, sender, timestamp: now });
}

function clearChat() {
    const messagesDiv = document.getElementById('chat-messages');
    messagesDiv.innerHTML = '<div class="text-muted">Start a conversation by asking a question about your knowledge graph...</div>';
    chatHistory = [];
}

// Graph visualization
function loadGraph() {
    const container = document.getElementById('graph-container');
    container.innerHTML = `
        <div class="d-flex align-items-center justify-content-center h-100 text-muted">
            <div class="text-center">
                <div class="spinner mb-3"></div>
                <p>Loading graph visualization...</p>
            </div>
        </div>
    `;
    
    // Simulate loading
    setTimeout(() => {
        container.innerHTML = `
            <div class="d-flex align-items-center justify-content-center h-100 text-muted">
                <div class="text-center">
                    <i class="fas fa-project-diagram fa-3x mb-3"></i>
                    <p>Graph visualization placeholder</p>
                    <small>Graph visualization will be implemented with a library like vis.js or d3.js</small>
                </div>
            </div>
        `;
    }, 2000);
}

// Utility functions
function showAlert(message, type = 'info') {
    // Create alert element
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    // Add to page
    const container = document.querySelector('.container-fluid');
    container.insertBefore(alertDiv, container.firstChild);
    
    // Auto-dismiss after 5 seconds
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

// Health check
async function checkHealth() {
    try {
        const response = await fetch('/healthcheck');
        return response.ok;
    } catch (error) {
        return false;
    }
}

// Initialize health check
setInterval(async () => {
    const isHealthy = await checkHealth();
    // You could add a status indicator to the UI here
}, 30000); // Check every 30 seconds
