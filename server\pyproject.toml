[tool.poetry]
name = "graph-service"
version = "0.1.0"
description = "Zep Graph service implementing Graphiti package"
authors = ["<PERSON> <<EMAIL>>"]
readme = "README.md"
packages = [{ include = "graph_service" }]


[tool.poetry.dependencies]
python = "^3.10"
fastapi = "^0.115.0"
graphiti-core = { path = "../" }
pydantic-settings = "^2.4.0"
uvicorn = "^0.30.6"
httpx = "^0.28.1"
mistralai = "^1.7.0"
aiohttp = "^3.10.0"
python-multipart = "^0.0.6"

[tool.poetry.group.dev.dependencies]
pydantic = "^2.8.2"
mypy = "^1.11.1"
pytest = "^8.3.2"
python-dotenv = "^1.0.1"
pytest-asyncio = "^0.24.0"
pytest-xdist = "^3.6.1"
ruff = "^0.6.2"
fastapi-cli = "^0.0.5"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.pytest.ini_options]
pythonpath = ["."]

[tool.ruff]
line-length = 100

[tool.ruff.lint]
select = [
    # pycodestyle
    "E",
    # Pyflakes
    "F",
    # pyupgrade
    "UP",
    # flake8-bugbear
    "B",
    # flake8-simplify
    "SIM",
    # isort
    "I",
]
ignore = ["E501"]

[tool.ruff.format]
quote-style = "single"
indent-style = "space"
docstring-code-format = true
