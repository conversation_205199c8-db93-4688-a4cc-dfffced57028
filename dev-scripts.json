{"scripts": {"setup": "Run setup script for your platform", "start-all": "docker-compose up -d", "stop-all": "docker-compose down", "restart-all": "docker-compose restart", "logs": "docker-compose logs -f", "logs-frontend": "docker-compose logs -f frontend", "logs-backend": "docker-compose logs -f graph", "logs-neo4j": "docker-compose logs -f neo4j", "reset-db": "docker-compose down -v && docker-compose up neo4j -d", "rebuild": "docker-compose build --no-cache && docker-compose up -d", "health-check": "Check service health", "frontend-dev": "cd frontend && npm start", "backend-dev": "cd server && poetry run uvicorn graph_service.main:app --reload"}, "urls": {"frontend": "http://localhost:3567", "backend": "http://localhost:8234", "api-docs": "http://localhost:8234/docs", "neo4j": "http://localhost:7892"}, "credentials": {"neo4j": {"username": "neo4j", "password": "Triathlon16"}}}