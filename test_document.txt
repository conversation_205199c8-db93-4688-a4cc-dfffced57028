This is a test document for the Graphiti knowledge graph system.

The document contains information about artificial intelligence and machine learning.
AI systems can process large amounts of data and extract meaningful patterns.
Machine learning algorithms learn from data to make predictions and decisions.

Key concepts:
- Neural networks are inspired by biological neurons
- Deep learning uses multiple layers of neural networks
- Natural language processing helps computers understand human language
- Computer vision enables machines to interpret visual information

This test document will be used to verify that the document upload and entity extraction pipeline works correctly with the new Flask frontend.
