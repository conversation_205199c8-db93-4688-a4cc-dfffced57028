#!/bin/bash

# Graphiti Full Stack Setup Script for Unix/Linux/macOS

echo "🚀 Setting up Graphiti Full Stack Application..."

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed. Please install Docker first."
    exit 1
fi

# Check if Docker Compose is available
if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose is not available. Please install Docker Compose."
    exit 1
fi

# Check if .env file exists
if [ ! -f ".env" ]; then
    echo "❌ .env file not found. Please create a .env file with required environment variables."
    exit 1
fi

echo "✅ Prerequisites check passed!"

# Build and start services
echo "🔨 Building and starting services..."
docker-compose down -v
docker-compose build --no-cache
docker-compose up -d

# Wait for services to be ready
echo "⏳ Waiting for services to start..."
sleep 30

# Check service health
echo "🔍 Checking service health..."

# Check Neo4j
if curl -f http://localhost:7892 > /dev/null 2>&1; then
    echo "✅ Neo4j is running on http://localhost:7892"
else
    echo "❌ Neo4j is not responding"
fi

# Check Backend API
if curl -f http://localhost:8234/healthcheck > /dev/null 2>&1; then
    echo "✅ Backend API is running on http://localhost:8234"
else
    echo "❌ Backend API is not responding"
fi

# Check Frontend
if curl -f http://localhost:3567 > /dev/null 2>&1; then
    echo "✅ Frontend is running on http://localhost:3567"
else
    echo "❌ Frontend is not responding"
fi

echo ""
echo "🎉 Setup complete! Your Graphiti application is ready!"
echo ""
echo "📋 Service URLs:"
echo "   • Frontend:     http://localhost:3567"
echo "   • Backend API:  http://localhost:8234"
echo "   • Neo4j Browser: http://localhost:7892"
echo ""
echo "🔧 Useful commands:"
echo "   • View logs:    docker-compose logs -f"
echo "   • Stop all:     docker-compose down"
echo "   • Restart:      docker-compose restart"
echo ""
echo "📖 Neo4j Login:"
echo "   • Username: neo4j"
echo "   • Password: Triathlon16"
