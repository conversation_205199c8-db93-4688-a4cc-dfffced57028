#!/usr/bin/env python3
"""
Test script to upload a document and test the complete processing pipeline.
"""

import requests
import time
import json

def test_document_upload():
    """Test the document upload and processing pipeline."""
    
    # Upload the test document
    print("🚀 Testing Document Processing Pipeline")
    print("=" * 50)
    
    # Check service status first
    print("\n1. Checking service status...")
    try:
        response = requests.get("http://localhost:8234/api/documents/services/status")
        if response.status_code == 200:
            status_data = response.json()
            print(f"   OCR Service: {'✅ Available' if status_data.get('ocr_service', {}).get('available') else '❌ Unavailable'}")
            print(f"   Entity Extraction: {'✅ Available' if status_data.get('entity_extraction', {}).get('available') else '❌ Unavailable'}")
            print(f"   Pipeline Status: {status_data.get('pipeline_status', 'unknown')}")
        else:
            print(f"   ❌ Service status check failed: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Error checking service status: {e}")
    
    # Upload the document
    print("\n2. Uploading test document...")
    try:
        with open("test_document.pdf", "rb") as f:
            files = {"file": ("test_document.pdf", f, "application/pdf")}
            data = {
                "group_id": "test_group",
                "upload_type": "messages"
            }
            
            response = requests.post(
                "http://localhost:8234/api/documents/upload",
                files=files,
                data=data,
                timeout=60
            )
            
            if response.status_code == 202:
                result = response.json()
                print(f"   ✅ Upload started successfully")
                print(f"   📄 Filename: {result.get('filename')}")
                print(f"   🔄 Status: {result.get('status')}")
                print(f"   ⚙️ Processing Method: {result.get('processing_method')}")
                
                # Wait for processing to complete
                print("\n3. Waiting for processing to complete...")
                time.sleep(10)  # Give it time to process
                
            else:
                print(f"   ❌ Upload failed: {response.status_code}")
                print(f"   Error: {response.text}")
                return
                
    except Exception as e:
        print(f"   ❌ Error uploading document: {e}")
        return
    
    # Check for episodes
    print("\n4. Checking for created episodes...")
    try:
        response = requests.get("http://localhost:8234/episodes/test_group?last_n=10")
        if response.status_code == 200:
            episodes = response.json()
            print(f"   📚 Found {len(episodes)} episodes")
            for i, episode in enumerate(episodes):
                print(f"   Episode {i+1}: {episode.get('name', 'Unknown')}")
                print(f"     Created: {episode.get('created_at', 'Unknown')}")
                print(f"     Content length: {len(episode.get('content', ''))}")
        else:
            print(f"   ❌ Failed to retrieve episodes: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Error retrieving episodes: {e}")
    
    # Search for entities
    print("\n5. Searching for extracted entities...")
    try:
        search_data = {
            "query": "curcumin turmeric",
            "group_ids": ["test_group"],
            "max_facts": 20
        }
        
        response = requests.post(
            "http://localhost:8234/search/nodes",
            json=search_data
        )
        
        if response.status_code == 200:
            nodes = response.json()
            print(f"   🔍 Found {len(nodes)} nodes related to 'curcumin turmeric'")
            for i, node in enumerate(nodes[:5]):  # Show first 5
                print(f"   Node {i+1}: {node.get('name', 'Unknown')} ({node.get('node_type', 'Unknown')})")
        else:
            print(f"   ❌ Failed to search nodes: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Error searching nodes: {e}")
    
    # Search for facts/relationships
    print("\n6. Searching for facts and relationships...")
    try:
        search_data = {
            "query": "anti-inflammatory",
            "group_ids": ["test_group"],
            "max_facts": 10
        }
        
        response = requests.post(
            "http://localhost:8234/search/facts",
            json=search_data
        )
        
        if response.status_code == 200:
            facts = response.json()
            print(f"   📊 Found {len(facts)} facts related to 'anti-inflammatory'")
            for i, fact in enumerate(facts[:3]):  # Show first 3
                print(f"   Fact {i+1}: {fact.get('fact', 'Unknown')}")
        else:
            print(f"   ❌ Failed to search facts: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Error searching facts: {e}")
    
    # Check for references (if the endpoint exists)
    print("\n7. Checking for extracted references...")
    try:
        # This endpoint might not exist in the current implementation
        response = requests.get("http://localhost:8234/api/references/test_group")
        if response.status_code == 200:
            references = response.json()
            print(f"   📚 Found {len(references)} references")
            for i, ref in enumerate(references[:3]):  # Show first 3
                print(f"   Reference {i+1}: {ref.get('title', 'Unknown')}")
        else:
            print(f"   ❌ References endpoint not available or no references found: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Error checking references: {e}")
    
    print("\n" + "=" * 50)
    print("🎯 Pipeline Test Complete!")
    print("\nNext steps:")
    print("- Check the Neo4j browser at http://localhost:7892")
    print("- Use the frontend at http://localhost:3567 to explore results")
    print("- Check the API documentation at http://localhost:8234/docs")

if __name__ == "__main__":
    test_document_upload()
