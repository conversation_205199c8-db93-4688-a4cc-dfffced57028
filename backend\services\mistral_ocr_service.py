"""
Mistral OCR Service for extracting text from documents and images.
"""
import os
import asyncio
import logging
from typing import Optional, Dict, Any
from mistralai import Mistral

logger = logging.getLogger(__name__)

class MistralOCRService:
    """Service for handling Mistral OCR operations."""
    
    def __init__(self):
        """Initialize the Mistral OCR service."""
        self.api_key = os.getenv("MISTRAL_API_KEY")
        if not self.api_key:
            logger.warning("MISTRAL_API_KEY not found in environment variables")
            self.client = None
        else:
            self.client = Mistral(api_key=self.api_key)
            logger.info("Mistral OCR service initialized successfully")
    
    async def extract_text(self, document_url: str, doc_type: str = "document_url") -> Optional[str]:
        """
        Extract text from a document using Mistral OCR.
        
        Args:
            document_url: Base64 encoded document URL or regular URL
            doc_type: Type of document ("document_url" or "image_url")
        
        Returns:
            Extracted text content or None if extraction fails
        """
        if not self.client:
            logger.error("Mistral client not initialized - missing API key")
            return None
        
        try:
            logger.info(f"Starting OCR extraction for document type: {doc_type}")
            
            # Prepare document payload
            if doc_type == "image_url":
                document_payload = {
                    "type": "image_url",
                    "image_url": document_url
                }
            else:
                document_payload = {
                    "type": "document_url", 
                    "document_url": document_url
                }
            
            # Run OCR processing in a thread pool to avoid blocking
            loop = asyncio.get_event_loop()
            ocr_response = await loop.run_in_executor(
                None,
                lambda: self.client.ocr.process(
                    model="mistral-ocr-latest",
                    document=document_payload,
                    include_image_base64=False  # We don't need images back
                )
            )
            
            # Extract text content from response
            if hasattr(ocr_response, 'content') and ocr_response.content:
                extracted_text = ocr_response.content
                logger.info(f"Successfully extracted {len(extracted_text)} characters")
                return extracted_text
            
            elif hasattr(ocr_response, 'text') and ocr_response.text:
                extracted_text = ocr_response.text
                logger.info(f"Successfully extracted {len(extracted_text)} characters")
                return extracted_text
            
            else:
                logger.warning("No text content found in OCR response")
                logger.debug(f"OCR response structure: {dir(ocr_response)}")
                return None
        
        except Exception as e:
            logger.error(f"Error during OCR extraction: {str(e)}")
            return None
    
    async def extract_text_with_metadata(self, document_url: str, doc_type: str = "document_url") -> Optional[Dict[str, Any]]:
        """
        Extract text and metadata from a document using Mistral OCR.
        
        Args:
            document_url: Base64 encoded document URL or regular URL
            doc_type: Type of document ("document_url" or "image_url")
        
        Returns:
            Dictionary containing text content and metadata, or None if extraction fails
        """
        if not self.client:
            logger.error("Mistral client not initialized - missing API key")
            return None
        
        try:
            logger.info(f"Starting OCR extraction with metadata for document type: {doc_type}")
            
            # Prepare document payload
            if doc_type == "image_url":
                document_payload = {
                    "type": "image_url",
                    "image_url": document_url
                }
            else:
                document_payload = {
                    "type": "document_url",
                    "document_url": document_url
                }
            
            # Run OCR processing in a thread pool to avoid blocking
            loop = asyncio.get_event_loop()
            ocr_response = await loop.run_in_executor(
                None,
                lambda: self.client.ocr.process(
                    model="mistral-ocr-latest",
                    document=document_payload,
                    include_image_base64=True  # Include images for metadata
                )
            )
            
            # Extract content and metadata
            result = {
                "text": None,
                "metadata": {},
                "images": [],
                "structure": {}
            }
            
            # Extract text content
            if hasattr(ocr_response, 'content') and ocr_response.content:
                result["text"] = ocr_response.content
            elif hasattr(ocr_response, 'text') and ocr_response.text:
                result["text"] = ocr_response.text
            
            # Extract metadata if available
            if hasattr(ocr_response, 'metadata'):
                result["metadata"] = ocr_response.metadata
            
            # Extract images if available
            if hasattr(ocr_response, 'images'):
                result["images"] = ocr_response.images
            
            # Extract structure information if available
            if hasattr(ocr_response, 'structure'):
                result["structure"] = ocr_response.structure
            
            if result["text"]:
                logger.info(f"Successfully extracted {len(result['text'])} characters with metadata")
                return result
            else:
                logger.warning("No text content found in OCR response")
                return None
        
        except Exception as e:
            logger.error(f"Error during OCR extraction with metadata: {str(e)}")
            return None
    
    def is_available(self) -> bool:
        """
        Check if the Mistral OCR service is available.
        
        Returns:
            True if service is available, False otherwise
        """
        return self.client is not None
    
    async def test_connection(self) -> bool:
        """
        Test the connection to Mistral OCR service.
        
        Returns:
            True if connection is successful, False otherwise
        """
        if not self.client:
            return False
        
        try:
            # Test with a simple image URL
            test_url = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="
            
            loop = asyncio.get_event_loop()
            response = await loop.run_in_executor(
                None,
                lambda: self.client.ocr.process(
                    model="mistral-ocr-latest",
                    document={
                        "type": "image_url",
                        "image_url": test_url
                    },
                    include_image_base64=False
                )
            )
            
            return True
        
        except Exception as e:
            logger.error(f"Mistral OCR connection test failed: {str(e)}")
            return False
    
    def get_supported_formats(self) -> Dict[str, list]:
        """
        Get the list of supported file formats.
        
        Returns:
            Dictionary with supported formats categorized by type
        """
        return {
            "images": ["png", "jpg", "jpeg", "avif"],
            "documents": ["pdf", "pptx", "docx"],
            "max_size_mb": 50,
            "max_pages": 1000
        }
